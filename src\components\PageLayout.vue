<template>
  <div class="page-layout">
    <!-- 页面头部 -->
    <div class="page-header" v-if="showHeader">
      <div class="header-content">
        <h1 class="page-title">{{ title }}</h1>
        <p v-if="subtitle" class="page-subtitle">{{ subtitle }}</p>
      </div>
      <div class="header-actions" v-if="$slots.actions">
        <slot name="actions"></slot>
      </div>
    </div>

    <!-- 页面内容 -->
    <div class="page-content" :class="{ 'no-header': !showHeader }">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title?: string;
  subtitle?: string;
  showHeader?: boolean;
}

withDefaults(defineProps<Props>(), {
  showHeader: true,
});
</script>

<style scoped>
.page-layout {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-8);
  padding-bottom: var(--spacing-6);
  border-bottom: 1px solid var(--border-color);
}

.header-content h1.page-title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.page-subtitle {
  color: var(--text-secondary);
  font-size: var(--font-size-base);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: var(--spacing-3);
  flex-shrink: 0;
}

.page-content {
  animation: fadeIn 0.6s ease-out;
}

.page-content.no-header {
  margin-top: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: var(--spacing-4);
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: flex-end;
  }
}

@media (max-width: 480px) {
  .page-layout {
    padding: 0 var(--spacing-4);
  }
}
</style>
