<template>
  <div class="statistics-container">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="数据看板" name="dashboard">
        <div class="statistics-page">
          <!-- Key Metrics -->
          <el-row :gutter="20">
            <el-col :span="6">
              <el-card class="kpi-card" shadow="hover">
                <div class="kpi-title">总营业额 (元)</div>
                <div class="kpi-value">
                  <count-up :end-val="keyMetrics.totalRevenue" :decimal-places="2"></count-up>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="kpi-card" shadow="hover">
                <div class="kpi-title">总订单数</div>
                <div class="kpi-value">
                  <count-up :end-val="keyMetrics.totalOrders"></count-up>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="kpi-card" shadow="hover">
                <div class="kpi-title">新增客户数</div>
                <div class="kpi-value">
                  <count-up :end-val="keyMetrics.newCustomers"></count-up>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="kpi-card" shadow="hover">
                <div class="kpi-title">客户满意度</div>
                <div class="kpi-value">
                  <count-up :end-val="keyMetrics.satisfactionRate" :decimal-places="1"></count-up>%
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- Charts -->
          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="16">
              <el-card shadow="hover">
                <v-chart class="chart" :option="lineChartOption" autoresize />
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card shadow="hover">
                <v-chart class="chart" :option="pieChartOption" autoresize />
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
      <el-tab-pane label="报告管理" name="reportManagement">
        <div class="report-management-page">
          <div style="text-align: right; margin-bottom: 20px;">
            <el-button type="primary" @click="openGenerateDialog">
              <el-icon><Plus /></el-icon>
              生成报告
            </el-button>
          </div>

          <el-table :data="statisticsList" stripe style="width: 100%">
            <el-table-column prop="statsId" label="报告ID" width="100"></el-table-column>
            <el-table-column prop="statsType" label="统计类型" width="180"></el-table-column>
            <el-table-column prop="statsPeriod" label="统计周期" width="120"></el-table-column>
            <el-table-column prop="statsDate" label="统计日期" width="180"></el-table-column>
            <el-table-column prop="createTime" label="生成时间" width="200"></el-table-column>
            <el-table-column label="操作" fixed="right" width="200">
              <template #default="{ row }">
                <el-button size="small" type="primary" @click="viewData(row)">查看数据</el-button>
                <el-button size="small" type="danger" @click="deleteReport(row.statsId)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- Dialog for viewing JSON data -->
    <el-dialog v-model="jsonDataDialogVisible" title="原始数据" width="50%">
      <pre>{{ currentJsonData }}</pre>
      <template #footer>
        <el-button @click="jsonDataDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- Dialog for generating new report -->
    <el-dialog v-model="generateDialogVisible" title="生成新报告" width="30%">
        <el-form :model="newReportForm" label-width="100px">
            <el-form-item label="统计类型">
                <el-select v-model="newReportForm.statsType" placeholder="请选择统计类型">
                    <el-option label="订单" value="订单"></el-option>
                    <el-option label="客户" value="客户"></el-option>
                    <el-option label="人员" value="人员"></el-option>
                    <el-option label="服务项目" value="服务项目"></el-option>
                    <el-option label="服务分布" value="服务分布"></el-option>
                    <el-option label="订单周报" value="订单周报"></el-option>
                    <el-option label="整体KPI" value="整体KPI"></el-option>
                </el-select>
            </el-form-item>
             <el-form-item label="统计周期">
                <el-select v-model="newReportForm.statsPeriod" placeholder="请选择统计周期">
                    <el-option label="日" value="日"></el-option>
                    <el-option label="周" value="周"></el-option>
                    <el-option label="月" value="月"></el-option>
                    <el-option label="年" value="年"></el-option>
                    <el-option label="总览" value="总览"></el-option>
                </el-select>
            </el-form-item>
        </el-form>
      <template #footer>
        <el-button @click="generateDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleGenerateReport">生成</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { PieChart, LineChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  ToolboxComponent,
} from 'echarts/components';
import VChart from 'vue-echarts';
import CountUp from 'vue-countup-v3';
import { Plus } from '@element-plus/icons-vue';

import { mockStatisticsList } from '@/api/mockData/system';
import type { Statistic } from '@/api/mockData/system';

use([
  CanvasRenderer,
  PieChart,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  ToolboxComponent
]);

const activeTab = ref('dashboard');

// --- Dashboard Data and Logic ---
const keyMetrics = ref({ totalRevenue: 0, totalOrders: 0, newCustomers: 0, satisfactionRate: 0 });
const lineChartOption = ref({});
const pieChartOption = ref({});

const loadDashboardData = () => {
  const keyMetricsData = statisticsList.value.find(s => s.statsType === '整体KPI');
  if (keyMetricsData) {
    keyMetrics.value = JSON.parse(keyMetricsData.data);
  }

  const weeklyChartData = statisticsList.value.find(s => s.statsType === '订单周报');
  if (weeklyChartData) {
    const parsedData = JSON.parse(weeklyChartData.data);
    lineChartOption.value = {
      title: { text: '近7日营业额与订单量' },
      tooltip: { trigger: 'axis' },
      legend: { data: ['营业额', '订单量'] },
      grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
      xAxis: { type: 'category', boundaryGap: false, data: parsedData.dates },
      yAxis: { type: 'value' },
      series: [
        { name: '营业额', type: 'line', data: parsedData.revenue },
        { name: '订单量', type: 'line', data: parsedData.orders },
      ],
    };
  }

  const serviceDistributionData = statisticsList.value.find(s => s.statsType === '服务分布');
  if (serviceDistributionData) {
    const parsedData = JSON.parse(serviceDistributionData.data);
    pieChartOption.value = {
      title: { text: '服务项目分布', left: 'center' },
      tooltip: { trigger: 'item', formatter: '{a} <br/>{b}: {c} ({d}%)' },
      legend: { orient: 'vertical', left: 'left', data: parsedData.map((item: any) => item.name) },
      series: [
        {
          name: '服务项目',
          type: 'pie',
          radius: '50%',
          center: ['50%', '60%'],
          data: parsedData,
          emphasis: {
            itemStyle: { shadowBlur: 10, shadowOffsetX: 0, shadowColor: 'rgba(0, 0, 0, 0.5)' },
          },
        },
      ],
    };
  }
};

// --- Report Management Logic ---
const statisticsList = ref<Statistic[]>([]);
const jsonDataDialogVisible = ref(false);
const currentJsonData = ref('');

const viewData = (row: Statistic) => {
  try {
    const parsed = JSON.parse(row.data);
    // Print fields to console as requested
    console.log('--- 报告数据详情 ---');
    console.log(parsed);
    console.log('--------------------');
    
    currentJsonData.value = JSON.stringify(parsed, null, 2);
    ElMessage.info('报告数据已打印到控制台，请按 F12 查看。');
  } catch (error) {
    currentJsonData.value = '无效的JSON数据';
    console.error("解析报告数据失败:", error);
  }
  jsonDataDialogVisible.value = true;
};

const deleteReport = (statsId: number) => {
   ElMessageBox.confirm('确定要删除这份报告吗?', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    const index = statisticsList.value.findIndex(r => r.statsId === statsId);
    if (index !== -1) {
      statisticsList.value.splice(index, 1);
      ElMessage.success('报告删除成功');
    }
  });
};

// --- Generate Report Logic ---
const generateDialogVisible = ref(false);
const newReportForm = reactive({
    statsType: '订单' as string,
    statsPeriod: '周' as '日' | '周' | '月' | '年' | '总览',
});

const openGenerateDialog = () => {
    generateDialogVisible.value = true;
};

const handleGenerateReport = () => {
    const newId = Math.max(...statisticsList.value.map(r => r.statsId), 0) + 1;
    
    let newData: object = {};
    // Mock data generation based on new types
    switch (newReportForm.statsType) {
        case '订单':
            newData = { count: Math.floor(Math.random() * 100), total_amount: Math.random() * 5000 };
            break;
        case '客户':
            newData = { new_users: Math.floor(Math.random() * 100), active_users: Math.floor(Math.random() * 1000) };
            break;
        case '人员':
            newData = { new_cleaners: Math.floor(Math.random() * 10), active_cleaners: Math.floor(Math.random() * 200) };
            break;
        case '服务项目':
            newData = {
              most_booked_service: {
                service_id: Math.floor(Math.random() * 10) + 1,
                service_name: '随机热门服务',
                category: '随机分类',
                booking_count: Math.floor(Math.random() * 200) + 50
              },
              least_booked_service: {
                service_id: Math.floor(Math.random() * 10) + 1,
                service_name: '随机冷门服务',
                category: '随机分类',
                booking_count: Math.floor(Math.random() * 10)
              }
            };
            break;
        // Cases for dashboard-specific types
        case '整体KPI':
             newData = {
                totalRevenue: Math.random() * 200000,
                totalOrders: Math.floor(Math.random() * 2000),
                newCustomers: Math.floor(Math.random() * 300),
                satisfactionRate: 90 + Math.random() * 10,
            };
            break;
        case '订单周报':
            newData = {
                dates: ['D1', 'D2', 'D3', 'D4', 'D5', 'D6', 'D7'],
                revenue: Array.from({length: 7}, () => Math.floor(Math.random() * 3000)),
                orders: Array.from({length: 7}, () => Math.floor(Math.random() * 100)),
            };
            break;
        case '服务分布':
             newData = [
                { value: Math.floor(Math.random() * 500), name: '日常保洁' },
                { value: Math.floor(Math.random() * 400), name: '深度除螨' },
                { value: Math.floor(Math.random() * 300), name: '家电清洗' },
            ];
            break;
    }

    const newReport: Statistic = {
        statsId: newId,
        statsType: newReportForm.statsType,
        statsPeriod: newReportForm.statsPeriod,
        statsDate: new Date().toISOString().split('T')[0],
        data: JSON.stringify(newData, null, 2),
        createTime: new Date().toLocaleString('sv-SE'),
    };

    statisticsList.value.unshift(newReport);
    ElMessage.success('新报告生成成功！');
    
    // Print the newly generated report to the console
    console.log('--- 新报告生成成功 ---');
    console.log('新生成的报告数据:', newReport);
    console.log('--------------------');

    generateDialogVisible.value = false;
    loadDashboardData(); // Refresh dashboard with potentially new data
};

onMounted(() => {
  statisticsList.value = JSON.parse(JSON.stringify(mockStatisticsList));
  loadDashboardData();
});
</script>

<style scoped>
.statistics-container {
  padding: 20px;
}
.statistics-page {
  /* removed padding to avoid double padding inside tab */
}
.report-management-page {
  /* removed padding to avoid double padding inside tab */
}
.kpi-card {
  text-align: center;
}
.kpi-title {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}
.kpi-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
}
.chart {
  height: 400px;
}
pre {
    background-color: #f4f4f5;
    border: 1px solid #e9e9eb;
    padding: 10px;
    border-radius: 4px;
    white-space: pre-wrap;
    word-wrap: break-word;
}
</style> 