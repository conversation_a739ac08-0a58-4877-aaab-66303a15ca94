/**
 * @description 公告信息
 */
export interface Notice {
  noticeId: number | string;
  adminId?: number; // 发布者ID
  title: string;
  content: string;
  noticeType: number; // 公告类型, e.g., 1:SYSTEM, 2:PROMOTION, 3:POLICY, 4:TRAINING
  status: 0 | 1; // 状态 0=隐藏, 1=发布
  publishTime?: string; // 发布时间
  expireTime?: string; // 过期时间
}

/**
 * @description 获取公告列表的查询参数
 */
export interface NoticeQueryParams {
  noticeType?: number;
  status?: 0 | 1;
} 