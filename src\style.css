:root {
  --primary-color: #007bff;
  --primary-color-hover: #0056b3;
  --background-gradient-start: #a1c4fd;
  --background-gradient-end: #c2e9fb;
  --form-background-color: rgba(255, 255, 255, 0.8);
  --text-color: #333;
  --border-color: #d9d9d9;
  --border-color-focus: #007bff;
  --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --font-family: 'Nunito', sans-serif;
}

body {
  margin: 0;
  font-family: var(--font-family);
  color: var(--text-color);
  background-image: linear-gradient(120deg, var(--background-gradient-start) 0%, var(--background-gradient-end) 100%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}

.auth-container {
  width: 100%;
  max-width: 400px;
  margin: 20px;
  padding: 40px;
  background: var(--form-background-color);
  border-radius: 12px;
  box-shadow: var(--box-shadow);
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

.auth-container .logo {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.auth-container h2 {
  margin-bottom: 2rem;
  color: var(--text-color);
  font-weight: 600;
}

.form-group {
  margin-bottom: 1.5rem;
  text-align: left;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.form-group input[type="text"],
.form-group input[type="password"],
.form-group input[type="email"],
.form-group input[type="tel"] {
  width: 100%;
  padding: 12px 15px;
  box-sizing: border-box;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  transition: border-color 0.3s, box-shadow 0.3s;
}

.form-group input:focus {
  outline: none;
  border-color: var(--border-color-focus);
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
}

.form-group button {
  width: 100%;
  padding: 12px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: background-color 0.3s;
}

.form-group button:hover {
  background-color: var(--primary-color-hover);
}

.auth-container p {
  margin-top: 1.5rem;
  font-size: 0.9rem;
}

.auth-container p a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
}

.auth-container p a:hover {
  text-decoration: underline;
}

.radio-group label {
  margin-right: 15px;
  font-weight: normal;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
}

.radio-group input {
  margin-right: 5px;
  accent-color: var(--primary-color);
}

/* Dashboard Styles */
.dashboard-container {
  width: 100%;
  max-width: 960px;
  margin: 40px auto;
  padding: 40px;
  background: var(--form-background-color);
  border-radius: 12px;
  box-shadow: var(--box-shadow);
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 20px;
  margin-bottom: 20px;
}

.dashboard-header h1 {
  margin: 0;
  font-size: 1.8rem;
  color: var(--primary-color);
}

.logout-btn {
  padding: 10px 20px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  transition: background-color 0.3s;
}

.logout-btn:hover {
  background-color: var(--primary-color-hover);
}

.dashboard-content {
  font-size: 1.1rem;
  line-height: 1.6;
}

.welcome-message {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: #e9f5ff;
  border-left: 5px solid var(--primary-color);
  border-radius: 6px;
}

.welcome-message p {
  margin: 0;
}

.task-filters {
  margin-bottom: 2rem;
  display: flex;
  gap: 1rem;
}

.task-filters button {
  padding: 10px 20px;
  border: 1px solid var(--border-color);
  background-color: transparent;
  color: var(--text-color);
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s;
}

.task-filters button:hover {
  background-color: #e9f5ff;
  border-color: var(--primary-color);
}

.task-filters button.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.loading-state, .error-state, .empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.error-state {
  color: #d9534f;
  background-color: #f2dede;
}

.booking-list {
  display: grid;
  gap: 1.5rem;
}

.booking-card {
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: box-shadow 0.3s, transform 0.3s;
}

.booking-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background-color: #f7f7f7;
  border-bottom: 1px solid var(--border-color);
}

.service-name {
  font-weight: 700;
  font-size: 1.2rem;
  color: var(--primary-color);
}

.status-badge {
  padding: 0.3rem 0.8rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  color: #fff;
}

.status-pending { background-color: #f0ad4e; }
.status-progress { background-color: #0275d8; }
.status-completed { background-color: #5cb85c; }
.status-cancelled { background-color: #6c757d; }
.status-unknown { background-color: #6c757d; }

.card-body {
  padding: 1.5rem;
}

.card-body p {
  margin: 0 0 0.8rem;
}

.card-body p:last-child {
  margin-bottom: 0;
}

.card-footer {
  padding: 1rem 1.5rem;
  text-align: right;
  border-top: 1px solid var(--border-color);
  background-color: #f7f7f7;
}

.action-btn {
  padding: 8px 16px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  transition: background-color 0.3s;
}

.action-btn:hover {
  background-color: var(--primary-color-hover);
}
