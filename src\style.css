:root {
  /* 主色调 - 现代蓝绿色系 */
  --primary-color: #4f46e5;
  --primary-color-hover: #3730a3;
  --primary-color-light: #e0e7ff;
  --secondary-color: #06b6d4;
  --secondary-color-hover: #0891b2;

  /* 背景色系 */
  --background-gradient-start: #667eea;
  --background-gradient-end: #764ba2;
  --background-light: #f8fafc;
  --background-white: #ffffff;
  --form-background-color: rgba(255, 255, 255, 0.95);

  /* 文字颜色 */
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  --text-white: #ffffff;

  /* 边框和分割线 */
  --border-color: #e2e8f0;
  --border-color-hover: #cbd5e1;
  --border-color-focus: #4f46e5;

  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* 状态颜色 */
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;

  /* 字体系统 */
  --font-family: 'Inter', 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;

  /* 间距系统 */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;

  /* 圆角系统 */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* 过渡动画 */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* 全局重置和基础样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--background-light);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

#app {
  min-height: 100vh;
  position: relative;
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--background-light);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color-hover);
  border-radius: var(--radius-lg);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* 认证页面样式 */
.auth-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--background-gradient-start) 0%, var(--background-gradient-end) 100%);
  padding: var(--spacing-4);
  position: relative;
  overflow: hidden;
}

.auth-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.auth-container {
  width: 100%;
  max-width: 420px;
  margin: 0 auto;
  padding: var(--spacing-10);
  background: var(--form-background-color);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  position: relative;
  z-index: 1;
  transform: translateY(0);
  transition: transform var(--transition-normal);
}

.auth-container:hover {
  transform: translateY(-2px);
}

/* 认证表单样式 */
.auth-header {
  margin-bottom: var(--spacing-8);
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: var(--spacing-4);
}

.logo-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-3);
  box-shadow: var(--shadow-lg);
}

.logo-icon svg {
  width: 32px;
  height: 32px;
  color: var(--text-white);
}

.logo-text {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--primary-color);
  margin: 0;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.auth-subtitle {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  margin: 0;
  font-weight: 500;
}

.auth-form {
  margin-bottom: var(--spacing-6);
}

.form-group {
  margin-bottom: var(--spacing-5);
  text-align: left;
}

.form-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-2);
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.label-icon {
  width: 16px;
  height: 16px;
  color: var(--text-secondary);
}

.input-wrapper {
  position: relative;
}

.form-input {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  transition: all var(--transition-normal);
  background: var(--background-white);
  color: var(--text-primary);
  font-family: var(--font-family);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-input.input-error {
  border-color: var(--error-color);
}

.form-input::placeholder {
  color: var(--text-muted);
}

.password-toggle {
  position: absolute;
  right: var(--spacing-3);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-1);
  color: var(--text-muted);
  transition: color var(--transition-fast);
}

.password-toggle:hover {
  color: var(--text-secondary);
}

.password-toggle svg {
  width: 20px;
  height: 20px;
}

.error-message {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--error-color);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-4);
  padding: var(--spacing-3);
  background: rgba(239, 68, 68, 0.1);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--error-color);
}

.error-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.auth-button {
  width: 100%;
  padding: var(--spacing-4);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--text-white);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  box-shadow: var(--shadow-md);
  font-family: var(--font-family);
}

.auth-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.auth-button:active {
  transform: translateY(0);
}

.auth-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.auth-footer {
  text-align: center;
}

.auth-link {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  margin: 0;
}

.link-primary {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
  transition: color var(--transition-fast);
}

.link-primary:hover {
  color: var(--primary-color-hover);
  text-decoration: underline;
}

/* 通用组件样式 */

/* 卡片组件 */
.card {
  background: var(--background-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: all var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--border-color);
  background: var(--background-light);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.card-body {
  padding: var(--spacing-6);
}

.card-footer {
  padding: var(--spacing-4) var(--spacing-6);
  border-top: 1px solid var(--border-color);
  background: var(--background-light);
}

/* 按钮组件 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-5);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  text-decoration: none;
  font-family: var(--font-family);
  white-space: nowrap;
  user-select: none;
}

.btn-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.btn-sm {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-xs);
}

.btn-lg {
  padding: var(--spacing-4) var(--spacing-6);
  font-size: var(--font-size-base);
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--text-white);
  box-shadow: var(--shadow-md);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: var(--background-white);
  color: var(--text-secondary);
  border: 2px solid var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.btn-success {
  background: var(--success-color);
  color: var(--text-white);
  box-shadow: var(--shadow-md);
}

.btn-success:hover:not(:disabled) {
  background: #059669;
  transform: translateY(-1px);
}

.btn-warning {
  background: var(--warning-color);
  color: var(--text-white);
  box-shadow: var(--shadow-md);
}

.btn-warning:hover:not(:disabled) {
  background: #d97706;
  transform: translateY(-1px);
}

.btn-danger {
  background: var(--error-color);
  color: var(--text-white);
  box-shadow: var(--shadow-md);
}

.btn-danger:hover:not(:disabled) {
  background: #dc2626;
  transform: translateY(-1px);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* 徽章组件 */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-xs);
  font-weight: 600;
  white-space: nowrap;
}

.badge-primary {
  background: var(--primary-color-light);
  color: var(--primary-color);
}

.badge-success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.badge-warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.badge-danger {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
}

.badge-info {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

/* 表单组件 */
.form-group {
  margin-bottom: var(--spacing-5);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-primary);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--spacing-3);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  transition: all var(--transition-normal);
  background: var(--background-white);
  color: var(--text-primary);
  font-family: var(--font-family);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-input:disabled,
.form-select:disabled,
.form-textarea:disabled {
  background: var(--background-light);
  color: var(--text-muted);
  cursor: not-allowed;
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: var(--text-muted);
}

.form-error {
  border-color: var(--error-color);
}

.form-error-text {
  display: block;
  margin-top: var(--spacing-1);
  font-size: var(--font-size-xs);
  color: var(--error-color);
}

/* 网格布局 */
.grid {
  display: grid;
  gap: var(--spacing-4);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

@media (max-width: 768px) {
  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: 1fr;
  }
}

/* 间距工具类 */
.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-1); }
.m-2 { margin: var(--spacing-2); }
.m-3 { margin: var(--spacing-3); }
.m-4 { margin: var(--spacing-4); }
.m-5 { margin: var(--spacing-5); }
.m-6 { margin: var(--spacing-6); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-1); }
.p-2 { padding: var(--spacing-2); }
.p-3 { padding: var(--spacing-3); }
.p-4 { padding: var(--spacing-4); }
.p-5 { padding: var(--spacing-5); }
.p-6 { padding: var(--spacing-6); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-1); }
.mb-2 { margin-bottom: var(--spacing-2); }
.mb-3 { margin-bottom: var(--spacing-3); }
.mb-4 { margin-bottom: var(--spacing-4); }
.mb-5 { margin-bottom: var(--spacing-5); }
.mb-6 { margin-bottom: var(--spacing-6); }

/* 文本工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-white { color: var(--text-white); }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }
.font-normal { font-weight: 400; }

/* 显示工具类 */
.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }

.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }

.justify-center { justify-content: center; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }

.gap-1 { gap: var(--spacing-1); }
.gap-2 { gap: var(--spacing-2); }
.gap-3 { gap: var(--spacing-3); }
.gap-4 { gap: var(--spacing-4); }
.gap-5 { gap: var(--spacing-5); }
.gap-6 { gap: var(--spacing-6); }

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

/* 动画类 */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.4s ease-out;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

/* 悬停效果 */
.hover-lift {
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.hover-scale {
  transition: transform var(--transition-normal);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: box-shadow var(--transition-normal);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(79, 70, 229, 0.3);
}

/* 加载状态 */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 骨架屏 */
.skeleton {
  background: linear-gradient(
    90deg,
    var(--background-light) 25%,
    var(--border-color) 50%,
    var(--background-light) 75%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.skeleton-text {
  height: 1em;
  border-radius: var(--radius-sm);
}

.skeleton-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.skeleton-button {
  height: 40px;
  border-radius: var(--radius-lg);
}

/* 响应式断点 */
@media (max-width: 1200px) {
  .container {
    max-width: 1140px;
  }
}

@media (max-width: 992px) {
  .container {
    max-width: 960px;
  }

  /* 隐藏桌面端元素 */
  .hidden-tablet {
    display: none !important;
  }
}

@media (max-width: 768px) {
  .container {
    max-width: 720px;
    padding: 0 var(--spacing-4);
  }

  /* 隐藏平板端元素 */
  .hidden-mobile {
    display: none !important;
  }

  /* 移动端显示 */
  .mobile-only {
    display: block !important;
  }

  /* 调整字体大小 */
  :root {
    --font-size-xs: 0.7rem;
    --font-size-sm: 0.8rem;
    --font-size-base: 0.9rem;
    --font-size-lg: 1rem;
    --font-size-xl: 1.1rem;
    --font-size-2xl: 1.3rem;
    --font-size-3xl: 1.6rem;
  }

  /* 调整间距 */
  .p-6 { padding: var(--spacing-4); }
  .m-6 { margin: var(--spacing-4); }
  .gap-6 { gap: var(--spacing-4); }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--spacing-3);
  }

  /* 超小屏幕调整 */
  .p-4 { padding: var(--spacing-3); }
  .m-4 { margin: var(--spacing-3); }
  .gap-4 { gap: var(--spacing-3); }

  /* 按钮在小屏幕上全宽 */
  .btn-mobile-full {
    width: 100%;
    justify-content: center;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }

  body {
    background: white !important;
    color: black !important;
  }

  .card {
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .auto-dark {
    --background-light: #1f2937;
    --background-white: #374151;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
    --border-color: #4b5563;
    --border-color-hover: #6b7280;
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .card {
    border-width: 2px;
  }

  .btn {
    border-width: 2px;
    border-style: solid;
  }

  .btn-primary {
    border-color: var(--primary-color);
  }

  .btn-secondary {
    border-color: var(--text-secondary);
  }
}

/* Dashboard Styles */
.dashboard-container {
  width: 100%;
  max-width: 960px;
  margin: 40px auto;
  padding: 40px;
  background: var(--form-background-color);
  border-radius: 12px;
  box-shadow: var(--box-shadow);
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 20px;
  margin-bottom: 20px;
}

.dashboard-header h1 {
  margin: 0;
  font-size: 1.8rem;
  color: var(--primary-color);
}

.logout-btn {
  padding: 10px 20px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  transition: background-color 0.3s;
}

.logout-btn:hover {
  background-color: var(--primary-color-hover);
}

.dashboard-content {
  font-size: 1.1rem;
  line-height: 1.6;
}

.welcome-message {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: #e9f5ff;
  border-left: 5px solid var(--primary-color);
  border-radius: 6px;
}

.welcome-message p {
  margin: 0;
}

.task-filters {
  margin-bottom: 2rem;
  display: flex;
  gap: 1rem;
}

.task-filters button {
  padding: 10px 20px;
  border: 1px solid var(--border-color);
  background-color: transparent;
  color: var(--text-color);
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s;
}

.task-filters button:hover {
  background-color: #e9f5ff;
  border-color: var(--primary-color);
}

.task-filters button.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.loading-state, .error-state, .empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.error-state {
  color: #d9534f;
  background-color: #f2dede;
}

.booking-list {
  display: grid;
  gap: 1.5rem;
}

.booking-card {
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: box-shadow 0.3s, transform 0.3s;
}

.booking-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background-color: #f7f7f7;
  border-bottom: 1px solid var(--border-color);
}

.service-name {
  font-weight: 700;
  font-size: 1.2rem;
  color: var(--primary-color);
}

.status-badge {
  padding: 0.3rem 0.8rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  color: #fff;
}

.status-pending { background-color: #f0ad4e; }
.status-progress { background-color: #0275d8; }
.status-completed { background-color: #5cb85c; }
.status-cancelled { background-color: #6c757d; }
.status-unknown { background-color: #6c757d; }

.card-body {
  padding: 1.5rem;
}

.card-body p {
  margin: 0 0 0.8rem;
}

.card-body p:last-child {
  margin-bottom: 0;
}

.card-footer {
  padding: 1rem 1.5rem;
  text-align: right;
  border-top: 1px solid var(--border-color);
  background-color: #f7f7f7;
}

.action-btn {
  padding: 8px 16px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  transition: background-color 0.3s;
}

.action-btn:hover {
  background-color: var(--primary-color-hover);
}
