# 家政人员前端模块 - 后端接口文档

本文档根据 `项目结构.md` 整理，列出了家政人员前端模块所使用的所有后端接口。

## 1. 身份认证 (Authentication)

### 1.1 用户登出

- **功能**: 清除用户会话，实现登出。
- **接口**: `POST /api/auth/logout`
- **请求体**: 无
- **响应**: 成功或失败的状态信息。

---

## 2. 个人中心 (Cleaner Profile)

### 2.1 获取家政人员详情

- **功能**: 用于在"个人资料"页面加载当前登录的家政人员的详细信息。
- **接口**: `GET /api/users/cleaners/{cleanerId}`
- **URL参数**: `cleanerId` (家政人员ID)
- **响应**: 包含家政人员详细信息的对象，如姓名、年龄、技能、资格证书图片URL等。

### 2.2 更新家政人员信息

- **功能**: 在"个人资料"页面，允许用户编辑并保存自己的信息。
- **接口**: `PUT /api/users/cleaners/{cleanerId}`
- **URL参数**: `cleanerId` (家政人员ID)
- **请求体 (Body)**:
  ```json
  {
    "realName": "string",
    "age": "number",
    "education": "string",
    "workYears": "number",
    "qualification": "Array<{name, url}>", // 资格证书图片列表
    "skills": "string[]",
    "avatar": "string" // 头像URL
  }
  ```
- **说明**: 请求体中只包含需要更新的字段。

---

## 3. 任务管理 (Tasks)

### 3.1 获取预约（任务）列表

- **功能**: 在"我的任务"页面，加载分配给当前家政人员的预约列表。
- **接口**: `GET /api/bookings`
- **查询参数 (Query Params)**:
  - `cleanerId`: (必需) 当前家政人员的ID。
  - `status`: 任务状态 (例如：0=待服务, 1=服务中, 2=已完成)。
  - `serviceDate`: 服务日期。
- **响应**: 预约任务对象数组。

### 3.2 更新预约状态

- **功能**: 在"我的任务"页面，允许家政人员更新任务状态（如"开始服务"、"完成服务"）。
- **接口**: `PUT /api/bookings/{bookingId}/status`
- **URL参数**: `bookingId` (预约ID)
- **请求体 (Body)**:
  ```json
  {
    "status": "number" // 例如：0-4 的数字
  }
  ```

---

## 4. 服务记录 (Service Records)

### 4.1 获取服务记录列表

- **功能**: 在"服务记录"页面，展示该家政人员相关的历史服务记录。
- **接口**: `GET /api/records`
- **查询参数 (Query Params)**:
  - `cleanerId`: (必需) 当前家政人员的ID。
- **响应**: 服务记录对象数组。

### 4.2 创建服务记录

- **功能**: 家政人员完成服务后，填写并提交服务详情、工作照片等信息。
- **接口**: `POST /api/records`
- **请求体 (Body)**:
  ```json
  {
    "bookingId": "number",
    "cleanerId": "number",
    "startTime": "string", // YYYY-MM-DD HH:mm:ss
    "endTime": "string",
    "serviceDetails": "string",
    "photos": "string[]", // 服务前照片URL列表
  }
  ```

---

## 5. 培训中心 (Training Center)

### 5.1 获取培训列表

- **功能**: 在"培训中心"页面，展示所有可参加或已参加的培训。
- **接口**: `GET /api/trainings`
- **查询参数 (Query Params)**:
  - `status`: 培训状态 (例如: PLANNED, ONGOING, COMPLETED)。
- **响应**: 培训课程对象数组。

### 5.2 报名参加培训

- **功能**: 家政人员在"培训中心"选择课程进行报名。
- **接口**: `POST /api/trainings/{trainingId}/register`
- **URL参数**: `trainingId` (培训ID)
- **请求体 (Body)**:
  ```json
  {
    "cleanerId": "number"
  }
  ```

### 5.3 取消培训报名

- **功能**: 家政人员取消已报名的培训。
- **接口**: `DELETE /api/trainings/{trainingId}/unregister`
- **URL参数**: `trainingId` (培训ID)
- **请求体 (Body)**:
  ```json
  {
    "cleanerId": "number"
  }
  ```

---

## 6. 客户评价 (Customer Feedback)

### 6.1 获取评价列表

- **功能**: 在"客户评价"页面，加载与当前家政人员相关的客户评价。
- **接口**: `GET /api/feedbacks`
- **查询参数 (Query Params)**:
  - `cleanerId`: (必需) 当前家政人员的ID。
  - `rating`: 按评分筛选。
- **响应**: 评价对象数组。

---

## 7. 公司公告 (Company Notices)

### 7.1 获取公告列表

- **功能**: 在"公司公告"页面，展示公司发布的通知和公告。
- **接口**: `GET /api/notices`
- **查询参数 (Query Params)**:
  - `noticeType`: 公告类型。
  - `status`: 状态 (1=已发布)。
- **响应**: 公告对象数组。

### 7.2 获取公告详情

- **功能**: 点击公告列表中的某一项，查看其完整内容。
- **接口**: `GET /api/notices/{noticeId}`
- **URL参数**: `noticeId` (公告ID)
- **响应**: 单个公告的详细信息对象。

---

## 8. 工作报告 (Work Report)

### 8.1 获取统计数据

- **功能**: 在"工作报告"页面，根据指定时间范围生成个人工作数据统计。
- **接口**: `GET /api/statistics`
- **查询参数 (Query Params)**:
  - `statsType`: "cleaner_report" (指定报告类型)
  - `cleanerId`: (必需) 当前家政人员的ID。
  - `startDate`: 报告周期的开始日期。
  - `endDate`: 报告周期的结束日期。
- **响应**: 包含总单数、总工时、平均分、服务类型分布等数据的统计对象。 