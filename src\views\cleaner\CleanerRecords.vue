<template>
  <div class="cleaner-records-page">
    <div class="page-header">
      <h1 class="page-title">服务记录</h1>
      <p class="page-subtitle">记录和管理您的服务过程</p>
    </div>

    <div class="records-content">
      <el-table :data="records" style="width: 100%">
        <el-table-column prop="date" label="服务日期" width="120" />
        <el-table-column prop="customer" label="客户" width="100" />
        <el-table-column prop="service" label="服务内容" />
        <el-table-column prop="duration" label="服务时长" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === '已完成' ? 'success' : 'warning'">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button text type="primary" size="small" @click="viewRecord(scope.row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ElMessage } from 'element-plus';

const records = ref([
  {
    id: 1,
    date: '2024-01-18',
    customer: '王女士',
    service: '家庭深度清洁',
    duration: '3小时',
    status: '已完成'
  },
  {
    id: 2,
    date: '2024-01-19',
    customer: '李先生',
    service: '日常保洁',
    duration: '2小时',
    status: '已完成'
  }
]);

const viewRecord = (record: any) => {
  ElMessage.info(`查看服务记录：${record.service}`);
};
</script>

<style scoped>
.cleaner-records-page {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #6b7280;
  margin: 0;
}

.records-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
}
</style>
