<template>
  <div class="customer-dashboard-page">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <div class="banner-content">
        <h1 class="welcome-title">欢迎使用家政服务</h1>
        <p class="welcome-subtitle">专业、贴心、可靠的家政服务，让您的生活更美好</p>
        <el-button type="primary" size="large" @click="$router.push('/customer/services')">
          立即预约服务
        </el-button>
      </div>
      <div class="banner-image">
        <img src="https://images.unsplash.com/photo-1581578731548-c64695cc6952?w=400" alt="家政服务" />
      </div>
    </div>

    <!-- 服务统计 -->
    <div class="service-stats">
      <div class="stat-card">
        <div class="stat-icon orders">
          <el-icon><List /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ totalOrders }}</div>
          <div class="stat-label">总订单数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon completed">
          <el-icon><Check /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ completedOrders }}</div>
          <div class="stat-label">已完成</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon rating">
          <el-icon><Star /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ averageRating }}</div>
          <div class="stat-label">平均评分</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon savings">
          <el-icon><Money /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">¥{{ totalSavings }}</div>
          <div class="stat-label">累计消费</div>
        </div>
      </div>
    </div>

    <!-- 热门服务 -->
    <div class="popular-services">
      <h2 class="section-title">热门服务</h2>
      <div class="service-grid">
        <div v-for="service in popularServices" :key="service.id" class="service-card" @click="bookService(service)">
          <div class="service-image">
            <img :src="service.image" :alt="service.name" />
          </div>
          <div class="service-content">
            <h3 class="service-name">{{ service.name }}</h3>
            <p class="service-description">{{ service.description }}</p>
            <div class="service-meta">
              <span class="service-price">¥{{ service.price }}/次</span>
              <el-rate v-model="service.rating" disabled size="small" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近订单 -->
    <div class="recent-orders">
      <div class="section-header">
        <h2 class="section-title">最近订单</h2>
        <el-button text type="primary" @click="$router.push('/customer/orders')">
          查看全部
        </el-button>
      </div>
      <div class="order-list">
        <div v-for="order in recentOrders" :key="order.id" class="order-item">
          <div class="order-info">
            <h3 class="order-title">{{ order.serviceName }}</h3>
            <p class="order-time">{{ order.serviceTime }}</p>
            <p class="order-cleaner">服务人员：{{ order.cleanerName }}</p>
          </div>
          <div class="order-status">
            <el-tag :type="getOrderStatusType(order.status)">{{ order.status }}</el-tag>
            <div class="order-actions">
              <el-button v-if="order.status === '已完成'" text type="primary" size="small">
                评价
              </el-button>
              <el-button v-if="order.status === '进行中'" text type="primary" size="small">
                查看详情
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 优质家政人员 -->
    <div class="featured-cleaners">
      <h2 class="section-title">优质家政人员</h2>
      <div class="cleaner-grid">
        <div v-for="cleaner in featuredCleaners" :key="cleaner.id" class="cleaner-card">
          <div class="cleaner-avatar">
            <img :src="cleaner.avatar" :alt="cleaner.name" />
          </div>
          <div class="cleaner-info">
            <h3 class="cleaner-name">{{ cleaner.name }}</h3>
            <p class="cleaner-experience">{{ cleaner.experience }}年经验</p>
            <div class="cleaner-rating">
              <el-rate v-model="cleaner.rating" disabled size="small" />
              <span class="rating-text">({{ cleaner.reviewCount }}条评价)</span>
            </div>
            <div class="cleaner-skills">
              <el-tag v-for="skill in cleaner.skills" :key="skill" size="small">{{ skill }}</el-tag>
            </div>
          </div>
          <div class="cleaner-actions">
            <el-button type="primary" size="small" @click="contactCleaner(cleaner)">
              预约服务
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import { List, Check, Star, Money } from '@element-plus/icons-vue';

// 响应式数据
const totalOrders = ref(28);
const completedOrders = ref(25);
const averageRating = ref(4.8);
const totalSavings = ref(3580);

// 热门服务数据
const popularServices = ref([
  {
    id: 1,
    name: '家庭深度清洁',
    description: '全屋深度清洁，包括厨房、卫生间、客厅等',
    price: 200,
    rating: 4.8,
    image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300'
  },
  {
    id: 2,
    name: '日常保洁',
    description: '定期上门保洁，维持家居清洁',
    price: 120,
    rating: 4.6,
    image: 'https://images.unsplash.com/photo-1527515637462-cff94eecc1ac?w=300'
  },
  {
    id: 3,
    name: '家电清洗',
    description: '专业清洗空调、洗衣机、冰箱等家电',
    price: 150,
    rating: 4.9,
    image: 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=300'
  },
  {
    id: 4,
    name: '开荒保洁',
    description: '新房装修后的全面清洁服务',
    price: 300,
    rating: 4.7,
    image: 'https://images.unsplash.com/photo-1581578731548-c64695cc6952?w=300'
  }
]);

// 最近订单数据
const recentOrders = ref([
  {
    id: 1,
    serviceName: '家庭深度清洁',
    serviceTime: '2024-01-18 14:00-17:00',
    cleanerName: '张阿姨',
    status: '已完成'
  },
  {
    id: 2,
    serviceName: '日常保洁',
    serviceTime: '2024-01-20 09:00-11:00',
    cleanerName: '李师傅',
    status: '进行中'
  },
  {
    id: 3,
    serviceName: '家电清洗',
    serviceTime: '2024-01-22 15:00-17:00',
    cleanerName: '王阿姨',
    status: '待服务'
  }
]);

// 优质家政人员数据
const featuredCleaners = ref([
  {
    id: 1,
    name: '张阿姨',
    experience: 5,
    rating: 4.9,
    reviewCount: 128,
    skills: ['深度清洁', '日常保洁', '厨房清洁'],
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100'
  },
  {
    id: 2,
    name: '李师傅',
    experience: 8,
    rating: 4.8,
    reviewCount: 95,
    skills: ['家电清洗', '开荒保洁', '地板打蜡'],
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100'
  },
  {
    id: 3,
    name: '王阿姨',
    experience: 3,
    rating: 4.7,
    reviewCount: 76,
    skills: ['日常保洁', '衣物整理', '厨房清洁'],
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100'
  }
]);

// 方法
const getOrderStatusType = (status: string) => {
  switch (status) {
    case '待服务': return 'warning';
    case '进行中': return 'primary';
    case '已完成': return 'success';
    default: return 'info';
  }
};

const bookService = (service: any) => {
  ElMessage.success(`预约服务：${service.name}`);
};

const contactCleaner = (cleaner: any) => {
  ElMessage.success(`联系家政人员：${cleaner.name}`);
};
</script>

<style scoped>
.customer-dashboard-page {
  max-width: 1200px;
  margin: 0 auto;
}

/* 欢迎横幅 */
.welcome-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 48px;
  border-radius: 16px;
  margin-bottom: 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
}

.banner-content {
  flex: 1;
  max-width: 500px;
}

.welcome-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 16px 0;
}

.welcome-subtitle {
  font-size: 18px;
  opacity: 0.9;
  margin: 0 0 24px 0;
  line-height: 1.6;
}

.banner-image {
  flex-shrink: 0;
  margin-left: 32px;
}

.banner-image img {
  width: 300px;
  height: 200px;
  object-fit: cover;
  border-radius: 12px;
}

/* 服务统计 */
.service-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 48px;
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.stat-icon.orders { background: #dbeafe; color: #2563eb; }
.stat-icon.completed { background: #d1fae5; color: #059669; }
.stat-icon.rating { background: #fef3c7; color: #d97706; }
.stat-icon.savings { background: #fce7f3; color: #be185d; }

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

/* 区域标题 */
.section-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 24px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

/* 热门服务 */
.popular-services {
  margin-bottom: 48px;
}

.service-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.service-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.service-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.service-image img {
  width: 100%;
  height: 160px;
  object-fit: cover;
}

.service-content {
  padding: 20px;
}

.service-name {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.service-description {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 16px 0;
  line-height: 1.5;
}

.service-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.service-price {
  font-size: 16px;
  font-weight: 600;
  color: #059669;
}

/* 最近订单 */
.recent-orders {
  margin-bottom: 48px;
}

.order-list {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f3f4f6;
}

.order-item:last-child {
  border-bottom: none;
}

.order-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.order-time,
.order-cleaner {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.order-status {
  text-align: right;
}

.order-actions {
  margin-top: 8px;
}

/* 优质家政人员 */
.featured-cleaners {
  margin-bottom: 48px;
}

.cleaner-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.cleaner-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 16px;
}

.cleaner-avatar img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}

.cleaner-info {
  flex: 1;
}

.cleaner-name {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.cleaner-experience {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 8px 0;
}

.cleaner-rating {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.rating-text {
  font-size: 12px;
  color: #9ca3af;
}

.cleaner-skills {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.cleaner-actions {
  display: flex;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-banner {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }
  
  .banner-image {
    margin-left: 0;
  }
  
  .banner-image img {
    width: 100%;
    max-width: 300px;
  }
  
  .service-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .order-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .order-status {
    width: 100%;
    text-align: left;
  }
  
  .cleaner-card {
    flex-direction: column;
    text-align: center;
  }
}
</style>
