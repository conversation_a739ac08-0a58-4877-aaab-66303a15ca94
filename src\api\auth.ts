import service from './index';
import type { UserLogin, UserRegister } from '@/types/user';

/**
 * @description 用户登录接口
 * @param {UserLogin} data - 登录信息
 * @returns {Promise<any>} - 成功时返回响应的 data 部分
 */
export const login = (data: UserLogin): Promise<any> => {
    return service({
        url: '/auth/login',
        method: 'post',
        data,
    });
};

/**
 * @description 用户注册接口
 * @param {UserRegister} data - 注册信息
 * @returns {Promise<any>}
 */
export const register = (data: UserRegister): Promise<any> => {
    return service({
        url: '/auth/register',
        method: 'post',
        data,
    });
};

/**
 * @description 获取当前用户信息接口
 * @returns {Promise<any>}
 */
export const getUserInfo = (): Promise<any> => {
    return service({
        url: '/auth/info',
        method: 'get',
    });
}; 