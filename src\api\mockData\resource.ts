export interface Resource {
  resourceId: number;
  resourceName: string;
  type: 1 | 2 | 3; // 1:清洁设备 2:工具 3:耗材
  brand: string | null;
  model: string | null;
  purchaseTime: string;
  status: 1 | 2 | 3 | 4; // 1:在用 2:闲置 3:维修中 4:报废
  location: string | null;
  remark: string | null;
  createTime: string;
}

export interface ResourceLog {
  logId: number;
  resourceId: number;
  cleanerId: number;
  checkoutTime: string;
  expectedReturnTime: string;
  actualReturnTime: string | null;
  status: '已领用' | '已归还'; // Log status is different from resource status
  notes: string;
}

export const mockResourceList: Resource[] = [
  {
    resourceId: 1,
    resourceName: '高压蒸汽清洗机 #1',
    type: 1,
    brand: 'Karcher',
    model: 'SC5',
    purchaseTime: '2023-01-15 10:00:00',
    status: 2, // 闲置
    location: 'A仓库-A01',
    remark: '配件齐全',
    createTime: '2023-01-15 10:00:00',
  },
    {
    resourceId: 2,
    resourceName: '高压蒸汽清洗机 #2',
    type: 1,
    brand: 'Karcher',
    model: 'SC5',
    purchaseTime: '2023-01-15 10:00:00',
    status: 1, // 在用
    location: 'A仓库-A01',
    remark: '外派中',
    createTime: '2023-01-15 10:00:00',
  },
  {
    resourceId: 3,
    resourceName: '专业玻璃清洁套装',
    type: 2,
    brand: 'Unger',
    model: 'Pro Kit',
    purchaseTime: '2023-03-20 14:30:00',
    status: 2, // 闲置
    location: 'B仓库-T01架',
    remark: '包含刮刀、涂水器、延长杆等。',
    createTime: '2023-03-20 14:30:00',
  },
   {
    resourceId: 4,
    resourceName: '地毯清洗机',
    type: 1,
    brand: 'Bissell',
    model: 'Big Green',
    purchaseTime: '2022-11-10 09:00:00',
    status: 3, // 维修中
    location: '维修间',
    remark: '电机故障，正在等待配件。',
    createTime: '2022-11-10 09:00:00',
  },
   {
    resourceId: 5,
    resourceName: '环保全能清洁剂(桶)',
    type: 3,
    brand: 'Eco-Max',
    model: 'All Purpose',
    purchaseTime: '2024-06-01 11:00:00',
    status: 2, // 闲置
    location: 'B仓库-C05架',
    remark: '5升装，可稀释使用。',
    createTime: '2024-06-01 11:00:00',
  },
];

export const mockResourceLogList: ResourceLog[] = [
    {
        logId: 101,
        resourceId: 2, // 高压蒸汽清洗机 #2
        cleanerId: 1, // 张芳
        checkoutTime: '2024-07-01 09:00:00',
        expectedReturnTime: '2024-07-01 17:00:00',
        actualReturnTime: null,
        status: '已领用',
        notes: '客户预约需要'
    },
]; 