<template>
  <div class="profile-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>个人资料</span>
          <div>
            <el-button v-if="!isEditing" type="primary" @click="toggleEdit(true)" :disabled="isLoading">编辑</el-button>
            <el-button v-if="isEditing" @click="cancelEdit">取消</el-button>
            <el-button v-if="isEditing" type="success" @click="saveProfile">保存</el-button>
          </div>
        </div>
      </template>

      <el-alert
        v-if="error"
        :title="error"
        type="warning"
        show-icon
        :closable="false"
        style="margin-bottom: 20px;"
      />

      <div v-if="isLoading" class="loading-state">正在加载资料...</div>

      <el-row v-else :gutter="30">
        <el-col :span="16">
          <el-form :model="profileForm" ref="formRef" label-width="100px" :disabled="!isEditing">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="真实姓名" prop="realName">
                  <el-input v-model="profileForm.realName"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="年龄" prop="age">
                  <el-input-number v-model="profileForm.age" :min="18" :max="65"></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="性别" prop="gender">
                  <el-radio-group v-model="profileForm.gender">
                    <el-radio :value="1">男</el-radio>
                    <el-radio :value="0">女</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="身份证号" prop="idCard">
                  <el-input v-model="profileForm.idCard" disabled></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="学历" prop="education">
                  <el-input v-model="profileForm.education"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="工作年限" prop="workYears">
                  <el-input-number v-model="profileForm.workYears" :min="0"></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="资格证书">
              <div v-if="!isEditing" class="qualification-display">
                <el-image
                  v-for="(cert, index) in profileForm.qualification"
                  :key="index"
                  :src="cert.url"
                  :preview-src-list="profileForm.qualification ? profileForm.qualification.map(c => c.url) : []"
                  :initial-index="index"
                  fit="cover"
                  class="qualification-img"
                />
                <span v-if="!profileForm.qualification || profileForm.qualification.length === 0">暂无</span>
              </div>
              <el-upload
                v-else
                v-model:file-list="profileForm.qualification"
                action="#"
                list-type="picture-card"
                :auto-upload="false"
              >
                <el-icon><Plus /></el-icon>
              </el-upload>
            </el-form-item>

            <el-form-item label="技能" prop="skills">
              <el-select v-model="profileForm.skills" multiple placeholder="请选择技能" style="width: 100%;">
                <el-option label="日常保洁" value="日常保洁"></el-option>
                <el-option label="深度保洁" value="深度保洁"></el-option>
                <el-option label="家电清洗" value="家电清洗"></el-option>
                <el-option label="做饭" value="做饭"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="8">
          <div class="avatar-uploader-container">
            <el-avatar :size="150" :src="profileForm.avatar" />
            <el-upload
              v-if="isEditing"
              class="avatar-uploader"
              action="#"
              :show-file-list="false"
              :before-upload="handleAvatarSuccess"
              :http-request="() => {}">
              <el-button type="primary" class="upload-btn">更换头像</el-button>
            </el-upload>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, type UploadProps } from 'element-plus';
import { getCleanerById, updateCleaner } from '../../api/user';
import type { Cleaner } from '../../types/user';
import { Plus } from '@element-plus/icons-vue';

const isEditing = ref(false);
const isLoading = ref(true);
const error = ref<string | null>(null);

// 假设我们能从登录状态中获取到 cleanerId
// 在当前模拟阶段，我们硬编码一个ID
const cleanerId = 1; 

const profileForm = reactive<Partial<Cleaner>>({});
const originalProfile = ref<Partial<Cleaner>>({});

const fetchProfile = async () => {
  isLoading.value = true;
  error.value = null;
  try {
    const data = await getCleanerById(cleanerId);
    Object.assign(profileForm, data);
    originalProfile.value = { ...data };
  } catch (err: any) {
    console.error("获取个人资料失败:", err);
    error.value = "获取个人资料失败，已为您加载模拟数据以供测试。";
    // 加载一些默认数据以便于UI展示和测试
    const mockData: Cleaner = {
      id: cleanerId,
      userId: 2,
      realName: '张三（模拟）', 
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
      age: 35,
      gender: '男',
      idCard: '110101199003078888',
      education: '高中',
      workYears: 5,
      skills: ['日常保洁', '深度保洁'],
      qualification: [
        { name: '高级家政师证.jpg', url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg' },
        { name: '健康证.jpg', url: 'https://fuss10.elemecdn.com/1/34/19aa98b1fcb2781c4fba33d850549jpeg.jpeg' }
      ],
      status: 0,
      rating: 4.8
    };
    Object.assign(profileForm, mockData);
    originalProfile.value = { ...mockData };
  } finally {
    isLoading.value = false;
  }
};

onMounted(fetchProfile);

const toggleEdit = (status: boolean) => {
  isEditing.value = status;
};

const cancelEdit = () => {
  Object.assign(profileForm, originalProfile.value);
  toggleEdit(false);
};

const saveProfile = async () => {
  try {
    if (!profileForm.id) {
        ElMessage.error('无法保存，用户ID缺失。');
        return;
    }
    // PUT请求将被拦截并打印到控制台
    await updateCleaner(profileForm.id, profileForm);
    ElMessage.success('个人资料已保存（模拟）！');
    originalProfile.value = { ...profileForm };
    toggleEdit(false);
  } catch (err: any) {
    if (err && !err.__MOCK__) { // 只显示真实错误
      ElMessage.error('保存失败');
    }
  }
};

const handleAvatarSuccess: UploadProps['beforeUpload'] = (rawFile) => {
  if (rawFile.type !== 'image/jpeg' && rawFile.type !== 'image/png') {
    ElMessage.error('头像图片只能是 JPG 或 PNG 格式!');
    return false;
  }
  // 模拟上传成功，用本地URL预览
  profileForm.avatar = URL.createObjectURL(rawFile);
  return false; // 阻止真实上传
};

</script>

<style scoped>
.profile-container {
  padding: 20px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.avatar-uploader-container {
  text-align: center;
}
.upload-btn {
  margin-top: 20px;
}

.profile-form .el-form-item {
  margin-bottom: 24px;
}

.qualification-display {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.qualification-img {
  width: 100px;
  height: 100px;
  border-radius: 6px;
  border: 1px solid #dcdfe6;
}
</style> 