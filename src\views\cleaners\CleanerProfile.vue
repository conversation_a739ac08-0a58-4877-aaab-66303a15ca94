<template>
  <PageLayout title="个人资料" subtitle="管理您的个人信息和专业技能">
    <template #actions>
      <button
        v-if="!isEditing"
        class="btn btn-primary"
        @click="toggleEdit(true)"
        :disabled="isLoading"
      >
        <svg class="btn-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M18.5 2.50023C18.8978 2.1024 19.4374 1.87891 20 1.87891C20.5626 1.87891 21.1022 2.1024 21.5 2.50023C21.8978 2.89805 22.1213 3.43762 22.1213 4.00023C22.1213 4.56284 21.8978 5.1024 21.5 5.50023L12 15.0002L8 16.0002L9 12.0002L18.5 2.50023Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        编辑资料
      </button>
      <div v-if="isEditing" class="edit-actions">
        <button class="btn btn-secondary" @click="cancelEdit">
          <svg class="btn-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          取消
        </button>
        <button class="btn btn-success" @click="saveProfile">
          <svg class="btn-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <polyline points="20,6 9,17 4,12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          保存
        </button>
      </div>
    </template>

    <!-- 错误提示 -->
    <AlertMessage
      v-if="error"
      type="warning"
      :message="error"
      @close="error = null"
    />

    <!-- 加载状态 -->
    <LoadingSpinner v-if="isLoading" text="正在加载资料..." />

    <!-- 成功提示 -->
    <AlertMessage
      v-if="successMessage"
      type="success"
      :message="successMessage"
      :auto-close="true"
      @close="successMessage = ''"
    />

    <!-- 主要内容 -->
    <div v-else class="profile-content">
      <div class="profile-grid">
        <!-- 头像区域 -->
        <div class="avatar-section">
          <div class="avatar-container">
            <div class="avatar-wrapper">
              <img :src="profileForm.avatar || '/default-avatar.png'" alt="用户头像" class="avatar-image" />
              <div v-if="isEditing" class="avatar-overlay">
                <button class="avatar-upload-btn" @click="triggerAvatarUpload">
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M23 19C23 19.5304 22.7893 20.0391 22.4142 20.4142C22.0391 20.7893 21.5304 21 21 21H3C2.46957 21 1.96086 20.7893 1.58579 20.4142C1.21071 20.0391 1 19.5304 1 19V8C1 7.46957 1.21071 6.96086 1.58579 6.58579C1.96086 6.21071 2.46957 6 3 6H7L9 4H15L17 6H21C21.5304 6 22.0391 6.21071 22.4142 6.58579C22.7893 6.96086 23 7.46957 23 8V19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="12" cy="13" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </button>
              </div>
            </div>
            <input
              ref="avatarInput"
              type="file"
              accept="image/*"
              style="display: none"
              @change="handleAvatarChange"
            />
          </div>
          <div class="avatar-info">
            <h3 class="user-name">{{ profileForm.realName || '未设置姓名' }}</h3>
            <p class="user-role">家政服务人员</p>
            <div class="rating-display">
              <div class="stars">
                <svg v-for="i in 5" :key="i" class="star" :class="{ filled: i <= Math.floor(profileForm.rating || 0) }" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <span class="rating-text">{{ profileForm.rating || 0 }} 分</span>
            </div>
          </div>
        </div>

        <!-- 表单区域 -->
        <div class="form-section">
          <form class="profile-form">
            <!-- 基本信息 -->
            <div class="form-section-header">
              <h3>基本信息</h3>
            </div>

            <div class="form-grid">
              <div class="form-group">
                <label class="form-label">真实姓名</label>
                <input
                  v-model="profileForm.realName"
                  type="text"
                  class="form-input"
                  :disabled="!isEditing"
                  placeholder="请输入真实姓名"
                />
              </div>

              <div class="form-group">
                <label class="form-label">年龄</label>
                <input
                  v-model.number="profileForm.age"
                  type="number"
                  class="form-input"
                  :disabled="!isEditing"
                  min="18"
                  max="65"
                  placeholder="请输入年龄"
                />
              </div>

              <div class="form-group">
                <label class="form-label">性别</label>
                <div class="radio-group">
                  <label class="radio-option">
                    <input
                      v-model="profileForm.gender"
                      type="radio"
                      value="男"
                      :disabled="!isEditing"
                    />
                    <span class="radio-label">男</span>
                  </label>
                  <label class="radio-option">
                    <input
                      v-model="profileForm.gender"
                      type="radio"
                      value="女"
                      :disabled="!isEditing"
                    />
                    <span class="radio-label">女</span>
                  </label>
                </div>
              </div>

              <div class="form-group">
                <label class="form-label">身份证号</label>
                <input
                  v-model="profileForm.idCard"
                  type="text"
                  class="form-input"
                  disabled
                  placeholder="身份证号码"
                />
              </div>

              <div class="form-group">
                <label class="form-label">学历</label>
                <select
                  v-model="profileForm.education"
                  class="form-input"
                  :disabled="!isEditing"
                >
                  <option value="">请选择学历</option>
                  <option value="小学">小学</option>
                  <option value="初中">初中</option>
                  <option value="高中">高中</option>
                  <option value="中专">中专</option>
                  <option value="大专">大专</option>
                  <option value="本科">本科</option>
                  <option value="研究生">研究生</option>
                </select>
              </div>

              <div class="form-group">
                <label class="form-label">工作年限</label>
                <input
                  v-model.number="profileForm.workYears"
                  type="number"
                  class="form-input"
                  :disabled="!isEditing"
                  min="0"
                  placeholder="工作年限"
                />
              </div>
            </div>

            <!-- 专业技能 -->
            <div class="form-section-header">
              <h3>专业技能</h3>
            </div>

            <div class="form-group full-width">
              <label class="form-label">服务技能</label>
              <div class="skills-container">
                <div class="skill-tags">
                  <span
                    v-for="skill in availableSkills"
                    :key="skill"
                    class="skill-tag"
                    :class="{
                      active: profileForm.skills?.includes(skill),
                      disabled: !isEditing
                    }"
                    @click="toggleSkill(skill)"
                  >
                    {{ skill }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 资格证书 -->
            <div class="form-section-header">
              <h3>资格证书</h3>
            </div>

            <div class="form-group full-width">
              <div class="certificates-container">
                <div v-if="profileForm.qualification && profileForm.qualification.length > 0" class="certificate-grid">
                  <div
                    v-for="(cert, index) in profileForm.qualification"
                    :key="index"
                    class="certificate-item"
                  >
                    <img :src="cert.url" :alt="cert.name" class="certificate-image" @click="previewCertificate(cert.url)" />
                    <p class="certificate-name">{{ cert.name }}</p>
                  </div>
                </div>
                <div v-else class="no-certificates">
                  <svg class="no-data-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  <p>暂无资格证书</p>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </PageLayout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { getCleanerById, updateCleaner } from '../../api/user';
import type { Cleaner } from '../../types/user';
import PageLayout from '../../components/PageLayout.vue';
import LoadingSpinner from '../../components/LoadingSpinner.vue';
import AlertMessage from '../../components/AlertMessage.vue';

const isEditing = ref(false);
const isLoading = ref(true);
const error = ref<string | null>(null);
const successMessage = ref('');
const avatarInput = ref<HTMLInputElement>();

// 假设我们能从登录状态中获取到 cleanerId
// 在当前模拟阶段，我们硬编码一个ID
const cleanerId = 1;

const profileForm = reactive<Partial<Cleaner>>({});
const originalProfile = ref<Partial<Cleaner>>({});

// 可选技能列表
const availableSkills = [
  '日常保洁', '深度保洁', '家电清洗', '做饭',
  '洗衣熨烫', '照顾老人', '照顾儿童', '宠物照料'
];

const fetchProfile = async () => {
  isLoading.value = true;
  error.value = null;
  try {
    const data = await getCleanerById(cleanerId);
    Object.assign(profileForm, data);
    originalProfile.value = { ...data };
  } catch (err: any) {
    console.error("获取个人资料失败:", err);
    error.value = "获取个人资料失败，已为您加载模拟数据以供测试。";
    // 加载一些默认数据以便于UI展示和测试
    const mockData: Cleaner = {
      id: cleanerId,
      userId: 2,
      realName: '张三（模拟）', 
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
      age: 35,
      gender: '男',
      idCard: '110101199003078888',
      education: '高中',
      workYears: 5,
      skills: ['日常保洁', '深度保洁'],
      qualification: [
        { name: '高级家政师证.jpg', url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg' },
        { name: '健康证.jpg', url: 'https://fuss10.elemecdn.com/1/34/19aa98b1fcb2781c4fba33d850549jpeg.jpeg' }
      ],
      status: 0,
      rating: 4.8
    };
    Object.assign(profileForm, mockData);
    originalProfile.value = { ...mockData };
  } finally {
    isLoading.value = false;
  }
};

onMounted(fetchProfile);

const toggleEdit = (status: boolean) => {
  isEditing.value = status;
};

const cancelEdit = () => {
  Object.assign(profileForm, originalProfile.value);
  toggleEdit(false);
};

const saveProfile = async () => {
  try {
    if (!profileForm.id) {
        error.value = '无法保存，用户ID缺失。';
        return;
    }
    // PUT请求将被拦截并打印到控制台
    await updateCleaner(profileForm.id, profileForm);
    // 显示成功消息
    successMessage.value = '个人资料已保存成功！';
    originalProfile.value = { ...profileForm };
    toggleEdit(false);
  } catch (err: any) {
    if (err && !err.__MOCK__) { // 只显示真实错误
      error.value = '保存失败，请稍后重试';
    } else {
      // 模拟成功
      successMessage.value = '个人资料已保存成功！';
      originalProfile.value = { ...profileForm };
      toggleEdit(false);
    }
  }
};

// 技能切换
const toggleSkill = (skill: string) => {
  if (!isEditing.value) return;

  if (!profileForm.skills) {
    profileForm.skills = [];
  }

  const index = profileForm.skills.indexOf(skill);
  if (index > -1) {
    profileForm.skills.splice(index, 1);
  } else {
    profileForm.skills.push(skill);
  }
};

// 头像上传相关
const triggerAvatarUpload = () => {
  if (avatarInput.value) {
    avatarInput.value.click();
  }
};

const handleAvatarChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];

  if (!file) return;

  if (file.type !== 'image/jpeg' && file.type !== 'image/png') {
    error.value = '头像图片只能是 JPG 或 PNG 格式!';
    return;
  }

  // 模拟上传成功，用本地URL预览
  profileForm.avatar = URL.createObjectURL(file);
};

// 证书预览
const previewCertificate = (url: string) => {
  window.open(url, '_blank');
};

</script>

<style scoped>
.profile-container {
  max-width: 1200px;
  margin: 0 auto;
}

.edit-actions {
  display: flex;
  gap: var(--spacing-3);
}

.alert-warning {
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid var(--warning-color);
  color: #92400e;
}

.alert-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-16);
  text-align: center;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  margin-bottom: var(--spacing-4);
  animation: spin 1s linear infinite;
}

.loading-spinner svg {
  width: 100%;
  height: 100%;
  color: var(--primary-color);
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 主要内容区域 */
.profile-content {
  background: var(--background-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
}

.profile-grid {
  display: grid;
  grid-template-columns: 300px 1fr;
  min-height: 600px;
}

/* 头像区域 */
.avatar-section {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--text-white);
  padding: var(--spacing-8);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.avatar-container {
  margin-bottom: var(--spacing-6);
}

.avatar-wrapper {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid rgba(255, 255, 255, 0.2);
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.avatar-wrapper:hover .avatar-overlay {
  opacity: 1;
}

.avatar-upload-btn {
  background: none;
  border: none;
  color: var(--text-white);
  cursor: pointer;
  padding: var(--spacing-2);
}

.avatar-upload-btn svg {
  width: 24px;
  height: 24px;
}

.avatar-info h3.user-name {
  font-size: var(--font-size-xl);
  font-weight: 700;
  margin: 0 0 var(--spacing-2) 0;
}

.user-role {
  color: rgba(255, 255, 255, 0.8);
  font-size: var(--font-size-sm);
  margin: 0 0 var(--spacing-4) 0;
}

.rating-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);
}

.stars {
  display: flex;
  gap: var(--spacing-1);
}

.star {
  width: 16px;
  height: 16px;
  fill: none;
  stroke: rgba(255, 255, 255, 0.5);
}

.star.filled {
  fill: #fbbf24;
  stroke: #fbbf24;
}

.rating-text {
  font-size: var(--font-size-sm);
  color: rgba(255, 255, 255, 0.8);
}

/* 表单区域 */
.form-section {
  padding: var(--spacing-8);
}

.form-section-header {
  margin-bottom: var(--spacing-6);
  padding-bottom: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
}

.form-section-header h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.profile-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-8);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-6);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-label {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-primary);
}

.form-input {
  padding: var(--spacing-3);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  transition: all var(--transition-normal);
  background: var(--background-white);
  color: var(--text-primary);
  font-family: var(--font-family);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-input:disabled {
  background: var(--background-light);
  color: var(--text-muted);
  cursor: not-allowed;
}

.form-input::placeholder {
  color: var(--text-muted);
}

/* 单选按钮组 */
.radio-group {
  display: flex;
  gap: var(--spacing-4);
}

.radio-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
}

.radio-option input[type="radio"] {
  width: 16px;
  height: 16px;
  accent-color: var(--primary-color);
}

.radio-option input[type="radio"]:disabled {
  cursor: not-allowed;
}

.radio-label {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
}

/* 技能标签 */
.skills-container {
  margin-top: var(--spacing-2);
}

.skill-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
}

.skill-tag {
  padding: var(--spacing-2) var(--spacing-4);
  background: var(--background-light);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  user-select: none;
}

.skill-tag:hover:not(.disabled) {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.skill-tag.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--text-white);
}

.skill-tag.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* 证书区域 */
.certificates-container {
  margin-top: var(--spacing-2);
}

.certificate-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: var(--spacing-4);
}

.certificate-item {
  text-align: center;
}

.certificate-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
  border-radius: var(--radius-lg);
  border: 2px solid var(--border-color);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.certificate-image:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.certificate-name {
  margin-top: var(--spacing-2);
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin-bottom: 0;
}

.no-certificates {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8);
  text-align: center;
  color: var(--text-muted);
}

.no-data-icon {
  width: 48px;
  height: 48px;
  margin-bottom: var(--spacing-3);
  opacity: 0.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-grid {
    grid-template-columns: 1fr;
  }

  .avatar-section {
    padding: var(--spacing-6);
  }

  .form-section {
    padding: var(--spacing-6);
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .page-header {
    flex-direction: column;
    gap: var(--spacing-4);
    align-items: stretch;
  }

  .header-actions {
    justify-content: flex-end;
  }
}

@media (max-width: 480px) {
  .profile-container {
    padding: var(--spacing-4);
  }

  .avatar-section {
    padding: var(--spacing-4);
  }

  .form-section {
    padding: var(--spacing-4);
  }

  .skill-tags {
    gap: var(--spacing-1);
  }

  .skill-tag {
    padding: var(--spacing-1) var(--spacing-3);
    font-size: var(--font-size-xs);
  }
}
</style>