<template>
  <div class="cleaner-dashboard-page">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <h1 class="welcome-title">欢迎回来，张三</h1>
        <p class="welcome-subtitle">今天是 {{ currentDate }}，祝您工作愉快！</p>
      </div>
      <div class="welcome-stats">
        <div class="stat-item">
          <div class="stat-number">{{ todayTasks }}</div>
          <div class="stat-label">今日任务</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ completedTasks }}</div>
          <div class="stat-label">已完成</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ rating }}</div>
          <div class="stat-label">评分</div>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <h2 class="section-title">快速操作</h2>
      <div class="action-grid">
        <div class="action-card" @click="$router.push('/cleaner/tasks')">
          <el-icon class="action-icon"><List /></el-icon>
          <h3>查看任务</h3>
          <p>查看今日待完成的任务</p>
        </div>
        <div class="action-card" @click="$router.push('/cleaner/records')">
          <el-icon class="action-icon"><Document /></el-icon>
          <h3>记录服务</h3>
          <p>记录服务过程和结果</p>
        </div>
        <div class="action-card" @click="$router.push('/cleaner/training')">
          <el-icon class="action-icon"><Reading /></el-icon>
          <h3>培训学习</h3>
          <p>参加培训提升技能</p>
        </div>
        <div class="action-card" @click="$router.push('/cleaner/feedback')">
          <el-icon class="action-icon"><ChatDotSquare /></el-icon>
          <h3>客户评价</h3>
          <p>查看客户反馈</p>
        </div>
      </div>
    </div>

    <!-- 今日任务概览 -->
    <div class="today-tasks">
      <h2 class="section-title">今日任务</h2>
      <div class="task-list">
        <div v-for="task in tasks" :key="task.id" class="task-item">
          <div class="task-info">
            <h3 class="task-title">{{ task.title }}</h3>
            <p class="task-address">{{ task.address }}</p>
            <div class="task-meta">
              <span class="task-time">{{ task.time }}</span>
              <el-tag :type="getTaskStatusType(task.status)">{{ task.status }}</el-tag>
            </div>
          </div>
          <div class="task-actions">
            <el-button v-if="task.status === '待开始'" type="primary" size="small">
              开始任务
            </el-button>
            <el-button v-if="task.status === '进行中'" type="success" size="small">
              完成任务
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 最新公告 -->
    <div class="latest-notices">
      <h2 class="section-title">最新公告</h2>
      <div class="notice-list">
        <div v-for="notice in notices" :key="notice.id" class="notice-item">
          <div class="notice-content">
            <h3 class="notice-title">{{ notice.title }}</h3>
            <p class="notice-summary">{{ notice.summary }}</p>
            <span class="notice-date">{{ notice.date }}</span>
          </div>
          <el-button text type="primary" @click="viewNotice(notice)">
            查看详情
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { List, Document, Reading, ChatDotSquare } from '@element-plus/icons-vue';

// 响应式数据
const todayTasks = ref(5);
const completedTasks = ref(3);
const rating = ref(4.8);

// 当前日期
const currentDate = computed(() => {
  return new Date().toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  });
});

// 任务数据
const tasks = ref([
  {
    id: 1,
    title: '家庭深度清洁',
    address: '北京市朝阳区xxx小区',
    time: '09:00-12:00',
    status: '待开始'
  },
  {
    id: 2,
    title: '办公室日常保洁',
    address: '北京市海淀区xxx大厦',
    time: '14:00-16:00',
    status: '进行中'
  },
  {
    id: 3,
    title: '家电清洗服务',
    address: '北京市西城区xxx小区',
    time: '16:30-18:00',
    status: '待开始'
  }
]);

// 公告数据
const notices = ref([
  {
    id: 1,
    title: '关于提升服务质量的通知',
    summary: '为了更好地为客户提供优质服务，请各位家政人员...',
    date: '2024-01-15'
  },
  {
    id: 2,
    title: '春节放假安排',
    summary: '根据国家法定节假日安排，春节期间的工作安排如下...',
    date: '2024-01-10'
  }
]);

// 获取任务状态类型
const getTaskStatusType = (status: string) => {
  switch (status) {
    case '待开始':
      return 'warning';
    case '进行中':
      return 'primary';
    case '已完成':
      return 'success';
    default:
      return 'info';
  }
};

// 查看公告详情
const viewNotice = (notice: any) => {
  ElMessage.info(`查看公告：${notice.title}`);
};
</script>

<style scoped>
.cleaner-dashboard-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
}

/* 欢迎区域 */
.welcome-section {
  background: linear-gradient(135deg, #4f46e5 0%, #06b6d4 100%);
  color: white;
  padding: 32px;
  border-radius: 16px;
  margin-bottom: 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.welcome-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
}

.welcome-stats {
  display: flex;
  gap: 32px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
}

/* 区域标题 */
.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 20px 0;
}

/* 快速操作 */
.quick-actions {
  margin-bottom: 32px;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.action-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.action-icon {
  font-size: 32px;
  color: #4f46e5;
  margin-bottom: 16px;
}

.action-card h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.action-card p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

/* 今日任务 */
.today-tasks {
  margin-bottom: 32px;
}

.task-list {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f3f4f6;
}

.task-item:last-child {
  border-bottom: none;
}

.task-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.task-address {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 8px 0;
}

.task-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.task-time {
  font-size: 14px;
  color: #4b5563;
}

/* 最新公告 */
.latest-notices {
  margin-bottom: 32px;
}

.notice-list {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.notice-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f3f4f6;
}

.notice-item:last-child {
  border-bottom: none;
}

.notice-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.notice-summary {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 8px 0;
}

.notice-date {
  font-size: 12px;
  color: #9ca3af;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-section {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .welcome-stats {
    gap: 20px;
  }
  
  .action-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }
  
  .task-item,
  .notice-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .task-actions {
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
