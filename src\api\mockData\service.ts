export interface Service {
  serviceId: number;
  serviceName: string;
  category: string;
  description: string;
  basePrice: number;
  unit: string;
  duration: number; // in minutes
  status: number; // 0: inactive, 1: active
  createTime: string;
}

export interface ServicePrice {
  priceId: number;
  serviceId: number;
  priceType: string;
  price: number;
  effectiveTime: string;
  expireTime: string | null;
}

export const mockServiceList: Service[] = [
  {
    serviceId: 1,
    serviceName: '日常保洁套餐',
    category: '保洁',
    description: '2小时日常清洁',
    basePrice: 120.00,
    unit: '次',
    duration: 120,
    status: 1,
    createTime: '2023-10-01 10:00:00',
  },
  {
    serviceId: 2,
    serviceName: '深度除螨服务',
    category: '保洁',
    description: '针对床垫、沙发、地毯等',
    basePrice: 200.00,
    unit: '次',
    duration: 90,
    status: 1,
    createTime: '2023-09-15 11:20:00',
  },
  {
    serviceId: 3,
    serviceName: '空调清洗',
    category: '保洁',
    description: '专业挂式机清洗',
    basePrice: 150.00,
    unit: '台',
    duration: 60,
    status: 0,
    createTime: '2023-08-20 14:00:00',
  },
  {
    serviceId: 4,
    serviceName: '金牌月嫂',
    category: '保姆',
    description: '提供24小时新生儿护理',
    basePrice: 15000.00,
    unit: '月',
    duration: 43200, // 30 days
    status: 1,
    createTime: '2023-07-01 08:00:00',
  },
  {
    serviceId: 5,
    serviceName: '家庭收纳整理',
    category: '保洁',
    description: '专业整理师上门服务',
    basePrice: 300.00,
    unit: '小时',
    duration: 60,
    status: 1,
    createTime: '2023-06-10 16:45:00',
  },
];

export const mockServicePriceList: ServicePrice[] = [
  { priceId: 1, serviceId: 1, priceType: '普通价', price: 120.00, effectiveTime: '2025-06-17 11:46:00', expireTime: null },
  { priceId: 2, serviceId: 1, priceType: 'VIP会员价', price: 100.00, effectiveTime: '2025-06-17 11:46:00', expireTime: null },
  { priceId: 3, serviceId: 2, priceType: '普通价', price: 200.00, effectiveTime: '2025-06-17 11:46:00', expireTime: null },
  { priceId: 4, serviceId: 3, priceType: '普通价', price: 150.00, effectiveTime: '2025-06-17 11:46:00', expireTime: null },
  { priceId: 5, serviceId: 5, priceType: '活动价', price: 250.00, effectiveTime: '2024-06-01 00:00:00', expireTime: '2024-07-01 00:00:00' },
]; 