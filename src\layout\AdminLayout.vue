<template>
  <el-container class="layout-container">
    <el-aside width="200px">
      <el-scrollbar>
        <el-menu :router="true" :default-openeds="['User']">
          <div class="logo-container">家政服务管理平台</div>
          <template v-for="route in routes" :key="route.path">
            <!-- 单独的菜单项（如仪表盘） -->
            <el-menu-item v-if="!route.children" :index="`/admin/${route.path}`">
              <el-icon><component :is="route.meta?.icon" /></el-icon>
              <span>{{ route.meta?.title }}</span>
            </el-menu-item>
            <!-- 有多个子菜单的项目 -->
            <el-sub-menu v-else-if="route.children && route.children.length > 1" :index="route.name">
              <template #title>
                <el-icon><component :is="route.meta?.icon" /></el-icon>{{ route.meta?.title }}
              </template>
              <el-menu-item v-for="child in route.children" :key="child.path" :index="`/admin/${route.path}/${child.path}`">
                {{ child.meta?.title }}
              </el-menu-item>
            </el-sub-menu>
            <!-- 只有一个子菜单的项目 -->
            <el-menu-item v-else-if="route.children && route.children.length === 1" :index="`/admin/${route.path}/${route.children[0].path}`">
               <el-icon><component :is="route.meta?.icon" /></el-icon>
              <span>{{ route.meta?.title }}</span>
            </el-menu-item>
          </template>
        </el-menu>
      </el-scrollbar>
    </el-aside>

    <el-container>
      <el-header style="text-align: right; font-size: 12px">
        <div class="toolbar">
          <el-dropdown>
            <el-icon style="margin-right: 8px; margin-top: 1px"
              ><setting
            /></el-icon>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>查看信息</el-dropdown-item>
                <el-dropdown-item>退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <span>Admin</span>
        </div>
      </el-header>

      <el-main>
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import {
  Setting, House, User, Goods, ChatDotSquare,
  Calendar, Reading, Box
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

const router = useRouter()
// 获取管理员路由的子路由
const routes = ref(router.options.routes.find(r => r.path === '/admin')?.children || [])

</script>

<style scoped>
.layout-container {
  height: 100vh;
}
.el-aside {
  color: var(--el-text-color-primary);
  background: var(--el-color-primary-light-8);
}
.el-menu {
  border-right: none;
}
.el-main {
  padding: 20px;
}
.toolbar {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  right: 20px;
}
.logo-container {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  color: var(--el-color-primary);
}
</style> 