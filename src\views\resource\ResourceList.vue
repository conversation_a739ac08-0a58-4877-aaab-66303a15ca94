<template>
  <el-card class="page-card">
    <template #header>
      <div class="card-header">
        <span>资源列表</span>
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>
          添加资源
        </el-button>
      </div>
    </template>

    <el-table :data="resourceList" style="width: 100%" height="100%">
      <el-table-column prop="resourceName" label="资源名称" width="200" show-overflow-tooltip />
      <el-table-column label="分类" width="120">
         <template #default="{ row }">
            {{ typeMap[row.type] }}
        </template>
      </el-table-column>
      <el-table-column prop="brand" label="品牌" width="150" />
      <el-table-column prop="model" label="型号" width="150" />
      <el-table-column label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="statusMap[row.status].type">{{ statusMap[row.status].text }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="location" label="存放位置" width="180" />
      <el-table-column prop="purchaseTime" label="采购日期" width="180" />
      <el-table-column label="操作" fixed="right" width="150">
        <template #default="{ row }">
          <el-button size="small" type="primary" @click="handleEdit(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(row.resourceId)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-card>

  <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px" @close="resetForm">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="资源名称" prop="resourceName">
            <el-input v-model="form.resourceName" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="分类" prop="type">
            <el-select v-model="form.type">
              <el-option label="清洁设备" :value="1" />
              <el-option label="工具" :value="2" />
              <el-option label="耗材" :value="3" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
       <el-row>
        <el-col :span="12">
          <el-form-item label="品牌" prop="brand">
            <el-input v-model="form.brand" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
           <el-form-item label="型号" prop="model">
            <el-input v-model="form.model" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="存放位置" prop="location">
            <el-input v-model="form.location" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
           <el-form-item label="采购时间" prop="purchaseTime">
            <el-date-picker v-model="form.purchaseTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" style="width:100%" />
          </el-form-item>
        </el-col>
      </el-row>
       <el-form-item label="状态" prop="status">
        <el-select v-model="form.status">
          <el-option label="在用" :value="1" />
          <el-option label="闲置" :value="2" />
          <el-option label="维修中" :value="3" />
          <el-option label="报废" :value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" type="textarea" :rows="3" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitForm">提交</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { mockResourceList, type Resource } from '@/api/mockData/resource';
import { Plus } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus';

const resourceList = ref<Resource[]>([]);
const dialogVisible = ref(false);
const dialogTitle = ref('');
const formRef = ref<FormInstance>();

const getInitialFormState = (): Resource => ({
  resourceId: 0,
  resourceName: '',
  type: 2, // 默认工具
  brand: '',
  model: '',
  purchaseTime: new Date().toLocaleString('sv-SE').replace(' ', 'T'),
  status: 2, // 默认闲置
  location: '',
  remark: '',
  createTime: new Date().toLocaleString('sv-SE').replace(' ', 'T'),
});

let form = reactive<Resource>(getInitialFormState());

const rules = reactive<FormRules>({
  resourceName: [{ required: true, message: '请输入资源名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择分类', trigger: 'change' }],
  purchaseTime: [{ required: true, message: '请选择采购时间', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
});

const typeMap: { [key: number]: string } = {
    1: '清洁设备',
    2: '工具',
    3: '耗材',
};

const statusMap: { [key: number]: { text: string, type: 'warning' | 'success' | 'info' | 'danger' } } = {
    1: { text: '在用', type: 'warning' },
    2: { text: '闲置', type: 'success' },
    3: { text: '维修中', type: 'info' },
    4: { text: '报废', type: 'danger' },
};

onMounted(() => {
  resourceList.value = JSON.parse(JSON.stringify(mockResourceList));
});

const resetForm = () => {
  Object.assign(form, getInitialFormState());
  formRef.value?.clearValidate();
};

const handleCreate = () => {
  resetForm();
  dialogTitle.value = '添加新资源';
  dialogVisible.value = true;
};

const handleEdit = (row: Resource) => {
  dialogTitle.value = '编辑资源';
  Object.assign(form, row);
  dialogVisible.value = true;
};

const handleDelete = (id: number) => {
  ElMessageBox.confirm('确定要永久删除此资源吗?', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    const index = resourceList.value.findIndex(r => r.resourceId === id);
    if (index !== -1) {
      resourceList.value.splice(index, 1);
      ElMessage.success('删除成功');
    }
  });
};

const submitForm = async () => {
  if (!formRef.value) return;
  await formRef.value.validate((valid) => {
    if (valid) {
      if (form.resourceId) { // Edit
        const index = resourceList.value.findIndex(r => r.resourceId === form.resourceId);
        if (index !== -1) {
          resourceList.value[index] = { ...form };
          ElMessage.success('更新成功');
          console.log('资源更新成功:', JSON.parse(JSON.stringify(resourceList.value[index])));
        }
      } else { // Create
        const newResource: Resource = {
          ...form,
          resourceId: Math.max(...resourceList.value.map(r => r.resourceId), 0) + 1,
          createTime: new Date().toLocaleString('sv-SE'),
        };
        resourceList.value.unshift(newResource);
        ElMessage.success('创建成功');
        console.log('资源创建成功:', JSON.parse(JSON.stringify(newResource)));
      }
      dialogVisible.value = false;
    }
  });
};
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}
:deep(.el-card__body) {
  flex: 1;
  overflow: hidden;
}
.el-select {
    width: 100%;
}
</style> 