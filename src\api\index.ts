import axios from 'axios';

/**
 * @description 创建axios实例
 */
const service = axios.create({
  baseURL: '/api', // API 的 base_url
  timeout: 5000, // 请求超时时间
});

/**
 * @description 请求拦截器
 */
service.interceptors.request.use(
  (config) => {
    // 根据您的要求，拦截所有修改性操作(POST, PUT, DELETE)
    if (['post', 'put', 'delete'].includes(config.method?.toLowerCase() || '')) {
      console.log('--- 模拟提交 ---');
      console.log(`请求路径: ${config.url}`);
      console.log(`请求方法: ${config.method?.toUpperCase()}`);
      console.log('提交数据:', config.data);
      console.log('--------------------');

      // 返回一个成功的 Promise，但传递一个特殊信号，阻止真实请求
      // 并可以在前端模拟一个成功的响应
      return Promise.reject({
        __MOCK__: true,
        message: '模拟提交成功！数据已打印到控制台。'
      });
    }
    // 对于 GET 请求，仍然允许通过
    return config;
  },
  (error) => {
    console.log(error); // for debug
    return Promise.reject(error);
  }
);

/**
 * @description 响应拦截器
 */
service.interceptors.response.use(
  (response) => {
    const res = response.data;
    
    // 根据后端文档，业务成功码为 200 或 10000
    if ((response.status === 200 || response.status === 201) && (res.code === 200 || res.code === 10000)) {
        return res.data; // 成功时，直接返回 `data` 部分
    } else {
        // 业务错误，将后端返回的 message 作为错误信息抛出
        return Promise.reject(new Error(res.message || 'Error'));
    }
  },
  (error) => {
    // 如果是我们的模拟提交，则在UI上显示成功提示
    if (error && error.__MOCK__) {
      // ElMessage.success(error.message); // Element Plus 消息提示
      // 返回一个 resolved Promise 以免UI上显示错误
      return Promise.resolve();
    }
    console.log('err' + error); // for debug

    let message = error.message;
    // 尝试从后端返回的响应体中获取更具体的错误信息
    if (error.response && error.response.data && error.response.data.message) {
      message = error.response.data.message;
    }

    // 抛出一个带有更具体信息的错误，以便页面可以显示它
    return Promise.reject(new Error(message));
  }
);

export default service; 