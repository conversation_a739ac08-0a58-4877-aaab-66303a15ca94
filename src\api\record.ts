import service from './index';
import type { RecordQueryParams, ServiceRecord } from '../types/record';

/**
 * @description 获取服务记录列表
 * @param params 查询参数
 */
export const getRecords = (params?: RecordQueryParams): Promise<any> => {
  return service({
    url: '/records',
    method: 'get',
    params,
  });
};

/**
 * @description 创建服务记录
 * @param data 服务记录信息
 */
export const createRecord = (data: ServiceRecord): Promise<any> => {
  return service({
    url: '/records',
    method: 'post',
    data,
  });
}; 