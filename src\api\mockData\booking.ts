export interface Booking {
  bookingId: number;
  customerId: number;
  serviceId: number;
  cleanerId: number | null;
  bookingTime: string;
  serviceDate: string;
  serviceTime: string;
  endTime: string | null;
  address: string;
  customerPhone: string;
  status: number; // 0=待分配, 1=已分配, 2=进行中, 3=已完成, 4=已取消
  createTime: string;
  remark: string | null;
}

export const mockBookingList: Booking[] = [
  {
    bookingId: 201,
    customerId: 1,
    serviceId: 1,
    cleanerId: null,
    bookingTime: '2024-06-20 10:00:00',
    serviceDate: '2024-06-25',
    serviceTime: '09:00',
    endTime: '11:00',
    address: '幸福小区 A栋 1201室',
    customerPhone: '13800138001',
    status: 0, // 待分配
    createTime: '2024-06-20 10:00:15',
    remark: '家里有宠物，请注意。',
  },
  {
    bookingId: 202,
    customerId: 2,
    serviceId: 3,
    cleanerId: null,
    bookingTime: '2024-06-21 11:30:00',
    serviceDate: '2024-06-28',
    serviceTime: '14:00',
    endTime: '16:30',
    address: '希望花园 B座 502室',
    customerPhone: '13900139002',
    status: 0, // 待分配
    createTime: '2024-06-21 11:30:45',
    remark: null,
  },
  {
    bookingId: 203,
    customerId: 3,
    serviceId: 2,
    cleanerId: 1,
    bookingTime: '2024-06-19 08:00:00',
    serviceDate: '2024-06-22',
    serviceTime: '10:00',
    endTime: '12:00',
    address: '阳光海岸 3号楼 1803室',
    customerPhone: '13700137003',
    status: 1, // 已分配
    createTime: '2024-06-19 08:01:02',
    remark: '请准时到达。',
  },
  {
    bookingId: 204,
    customerId: 4,
    serviceId: 4,
    cleanerId: 2,
    bookingTime: '2024-06-18 16:45:00',
    serviceDate: '2024-06-20',
    serviceTime: '09:00',
    endTime: '18:00',
    address: '未来城 10栋 2501室',
    customerPhone: '13600136004',
    status: 2, // 进行中
    createTime: '2024-06-18 16:45:22',
    remark: '月嫂服务，需要住家。',
  },
  {
    bookingId: 205,
    customerId: 1,
    serviceId: 1,
    cleanerId: 3,
    bookingTime: '2024-06-15 12:00:00',
    serviceDate: '2024-06-16',
    serviceTime: '15:00',
    endTime: '17:00',
    address: '幸福小区 A栋 1201室',
    customerPhone: '13800138001',
    status: 3, // 已完成
    createTime: '2024-06-15 12:00:50',
    remark: null,
  },
    {
    bookingId: 206,
    customerId: 2,
    serviceId: 1,
    cleanerId: 4,
    bookingTime: '2024-06-14 09:20:00',
    serviceDate: '2024-06-15',
    serviceTime: '10:00',
    endTime: '12:00',
    address: '希望花园 B座 502室',
    customerPhone: '13900139002',
    status: 4, // 已取消
    createTime: '2024-06-14 09:21:10',
    remark: '客户来电取消',
  },
]; 