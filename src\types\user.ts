/**
 * @description 用户登录信息
 */
export interface UserLogin {
    username?: string;
    password?: string;
}

/**
 * @description 用户注册信息
 */
export interface UserRegister extends UserLogin {
    phone?: string;
    email?: string;
    /**
     * 用户类型
     * 1: 管理员
     * 2: 家政人员
     * 3: 客户
     */
    userType?: number;
}

/**
 * @description 用户信息
 */
export interface User extends UserRegister {
    id?: string | number;
    avatar?: string;
    status?: number;
}

/**
 * @description 家政人员信息
 */
export interface Cleaner {
    id?: number;
    userId: number;
    realName: string;
    avatar?: string;
    idCard: string;
    gender: '男' | '女';
    age: number;
    education: string;
    workYears: number;
    qualification: Array<{ name: string; url: string }>;
    skills: string[];
    /**
     * 工作状态
     * 0: 空闲
     * 1: 忙碌
     */
    status?: 0 | 1;
    rating?: number;
} 