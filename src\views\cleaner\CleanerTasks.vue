<template>
  <div class="cleaner-tasks-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">我的任务</h1>
        <p class="page-subtitle">管理和跟踪您的服务任务</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="refreshTasks">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 任务统计 -->
    <div class="task-stats">
      <div class="stat-card">
        <div class="stat-icon pending">
          <el-icon><Clock /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ pendingTasks }}</div>
          <div class="stat-label">待开始</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon progress">
          <el-icon><Loading /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ inProgressTasks }}</div>
          <div class="stat-label">进行中</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon completed">
          <el-icon><Check /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ completedTasks }}</div>
          <div class="stat-label">已完成</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon total">
          <el-icon><List /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ totalTasks }}</div>
          <div class="stat-label">总任务</div>
        </div>
      </div>
    </div>

    <!-- 任务筛选 -->
    <div class="task-filters">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="全部任务" name="all"></el-tab-pane>
        <el-tab-pane label="待开始" name="pending"></el-tab-pane>
        <el-tab-pane label="进行中" name="progress"></el-tab-pane>
        <el-tab-pane label="已完成" name="completed"></el-tab-pane>
      </el-tabs>
    </div>

    <!-- 任务列表 -->
    <div class="task-list">
      <div v-for="task in filteredTasks" :key="task.id" class="task-card">
        <div class="task-header">
          <div class="task-title-section">
            <h3 class="task-title">{{ task.title }}</h3>
            <el-tag :type="getTaskStatusType(task.status)" size="small">
              {{ task.status }}
            </el-tag>
          </div>
          <div class="task-priority">
            <el-tag :type="getPriorityType(task.priority)" size="small">
              {{ task.priority }}
            </el-tag>
          </div>
        </div>

        <div class="task-content">
          <div class="task-info">
            <div class="info-item">
              <el-icon><Location /></el-icon>
              <span>{{ task.address }}</span>
            </div>
            <div class="info-item">
              <el-icon><Clock /></el-icon>
              <span>{{ task.time }}</span>
            </div>
            <div class="info-item">
              <el-icon><User /></el-icon>
              <span>{{ task.customer }}</span>
            </div>
            <div class="info-item">
              <el-icon><Phone /></el-icon>
              <span>{{ task.phone }}</span>
            </div>
          </div>

          <div class="task-description">
            <h4>服务内容：</h4>
            <p>{{ task.description }}</p>
          </div>

          <div class="task-actions">
            <el-button 
              v-if="task.status === '待开始'" 
              type="primary" 
              @click="startTask(task)"
            >
              开始任务
            </el-button>
            <el-button 
              v-if="task.status === '进行中'" 
              type="success" 
              @click="completeTask(task)"
            >
              完成任务
            </el-button>
            <el-button 
              v-if="task.status === '已完成'" 
              type="info" 
              @click="viewTaskDetail(task)"
            >
              查看详情
            </el-button>
            <el-button 
              text 
              type="primary" 
              @click="contactCustomer(task)"
            >
              联系客户
            </el-button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredTasks.length === 0" class="empty-state">
        <el-icon class="empty-icon"><DocumentRemove /></el-icon>
        <h3>暂无任务</h3>
        <p>当前筛选条件下没有找到任务</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
  Refresh, Clock, Loading, Check, List, Location, 
  User, Phone, DocumentRemove 
} from '@element-plus/icons-vue';

// 响应式数据
const activeTab = ref('all');

// 任务数据
const tasks = ref([
  {
    id: 1,
    title: '家庭深度清洁',
    address: '北京市朝阳区xxx小区1号楼',
    time: '2024-01-20 09:00-12:00',
    customer: '王女士',
    phone: '138****1234',
    status: '待开始',
    priority: '高',
    description: '客厅、卧室、厨房、卫生间全面清洁，包括地面拖洗、家具擦拭、玻璃清洁等。'
  },
  {
    id: 2,
    title: '办公室日常保洁',
    address: '北京市海淀区xxx大厦15层',
    time: '2024-01-20 14:00-16:00',
    customer: '李经理',
    phone: '139****5678',
    status: '进行中',
    priority: '中',
    description: '办公区域地面清洁、桌面整理、垃圾清理、卫生间清洁。'
  },
  {
    id: 3,
    title: '家电清洗服务',
    address: '北京市西城区xxx小区3号楼',
    time: '2024-01-20 16:30-18:00',
    customer: '张先生',
    phone: '137****9012',
    status: '待开始',
    priority: '低',
    description: '空调、洗衣机、冰箱内外清洁，包括滤网清洗和消毒。'
  },
  {
    id: 4,
    title: '新房开荒保洁',
    address: '北京市丰台区xxx小区2号楼',
    time: '2024-01-19 10:00-15:00',
    customer: '赵女士',
    phone: '136****3456',
    status: '已完成',
    priority: '高',
    description: '新装修房屋全面清洁，包括建筑垃圾清理、玻璃清洁、地面打蜡等。'
  }
]);

// 计算属性
const pendingTasks = computed(() => tasks.value.filter(t => t.status === '待开始').length);
const inProgressTasks = computed(() => tasks.value.filter(t => t.status === '进行中').length);
const completedTasks = computed(() => tasks.value.filter(t => t.status === '已完成').length);
const totalTasks = computed(() => tasks.value.length);

const filteredTasks = computed(() => {
  if (activeTab.value === 'all') return tasks.value;
  const statusMap = {
    'pending': '待开始',
    'progress': '进行中',
    'completed': '已完成'
  };
  return tasks.value.filter(task => task.status === statusMap[activeTab.value as keyof typeof statusMap]);
});

// 方法
const getTaskStatusType = (status: string) => {
  switch (status) {
    case '待开始': return 'warning';
    case '进行中': return 'primary';
    case '已完成': return 'success';
    default: return 'info';
  }
};

const getPriorityType = (priority: string) => {
  switch (priority) {
    case '高': return 'danger';
    case '中': return 'warning';
    case '低': return 'info';
    default: return 'info';
  }
};

const handleTabChange = (tabName: string) => {
  activeTab.value = tabName;
};

const refreshTasks = () => {
  ElMessage.success('任务列表已刷新');
};

const startTask = async (task: any) => {
  try {
    await ElMessageBox.confirm('确认开始此任务吗？', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'info'
    });
    
    task.status = '进行中';
    ElMessage.success('任务已开始');
  } catch {
    // 用户取消
  }
};

const completeTask = async (task: any) => {
  try {
    await ElMessageBox.confirm('确认完成此任务吗？', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'success'
    });
    
    task.status = '已完成';
    ElMessage.success('任务已完成');
  } catch {
    // 用户取消
  }
};

const viewTaskDetail = (task: any) => {
  ElMessage.info(`查看任务详情：${task.title}`);
};

const contactCustomer = (task: any) => {
  ElMessage.info(`联系客户：${task.customer} (${task.phone})`);
};
</script>

<style scoped>
.cleaner-tasks-page {
  max-width: 1200px;
  margin: 0 auto;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #6b7280;
  font-size: 16px;
  margin: 0;
}

/* 任务统计 */
.task-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.stat-icon.pending { background: #fef3c7; color: #d97706; }
.stat-icon.progress { background: #dbeafe; color: #2563eb; }
.stat-icon.completed { background: #d1fae5; color: #059669; }
.stat-icon.total { background: #f3f4f6; color: #6b7280; }

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

/* 任务筛选 */
.task-filters {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  padding: 0 24px;
}

/* 任务列表 */
.task-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.task-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.task-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f3f4f6;
}

.task-title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.task-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.task-content {
  padding: 20px 24px;
}

.task-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #6b7280;
}

.info-item .el-icon {
  color: #9ca3af;
}

.task-description {
  margin-bottom: 20px;
}

.task-description h4 {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.task-description p {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
}

.task-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 64px;
  color: #d1d5db;
  margin-bottom: 16px;
}

.empty-state h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.empty-state p {
  font-size: 14px;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .task-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .task-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .task-info {
    grid-template-columns: 1fr;
  }
  
  .task-actions {
    flex-direction: column;
  }
}
</style>
