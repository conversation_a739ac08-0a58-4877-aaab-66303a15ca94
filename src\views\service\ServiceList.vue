<template>
  <el-card class="page-card">
    <template #header>
      <div class="card-header">
        <span>服务列表</span>
        <el-button class="button" type="primary" @click="handleAdd">新增服务</el-button>
      </div>
    </template>
    
    <el-table :data="serviceList" style="width: 100%" height="100%">
      <el-table-column prop="serviceId" label="ID" width="60" />
      <el-table-column prop="serviceName" label="服务名称" width="200" />
      <el-table-column prop="category" label="服务类别" width="150" />
      <el-table-column prop="description" label="服务描述" show-overflow-tooltip />
      <el-table-column prop="basePrice" label="基础价格" width="100" />
      <el-table-column prop="unit" label="计价单位" width="100" />
      <el-table-column prop="duration" label="标准时长(分)" width="120" />
      <el-table-column prop="status" label="状态" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
            {{ scope.row.status === 1 ? '上架' : '下架' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="180" />
       <el-table-column label="操作" width="150" fixed="right">
        <template #default="scope">
          <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-card>

  <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px">
    <el-form :model="serviceForm" :rules="rules" ref="serviceFormRef" label-width="110px">
      <el-form-item label="服务名称" prop="serviceName">
        <el-input v-model="serviceForm.serviceName" />
      </el-form-item>
      <el-form-item label="服务类别" prop="category">
        <el-input v-model="serviceForm.category" />
      </el-form-item>
       <el-form-item label="服务描述" prop="description">
        <el-input v-model="serviceForm.description" type="textarea" />
      </el-form-item>
       <el-form-item label="基础价格" prop="basePrice">
        <el-input-number v-model="serviceForm.basePrice" :min="0" :precision="2" />
      </el-form-item>
       <el-form-item label="计价单位" prop="unit">
        <el-input v-model="serviceForm.unit" />
      </el-form-item>
       <el-form-item label="标准时长(分钟)" prop="duration">
        <el-input-number v-model="serviceForm.duration" :min="0" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="serviceForm.status">
          <el-radio :label="1">上架</el-radio>
          <el-radio :label="0">下架</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { mockServiceList, type Service } from '@/api/mockData/service';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage, ElMessageBox } from 'element-plus';

const router = useRouter();
const serviceList = ref<Service[]>(mockServiceList);
const dialogVisible = ref(false);
const dialogTitle = ref('');
const dialogMode = ref<'add' | 'edit'>('add');
const serviceFormRef = ref<FormInstance>();

const initialFormState: Omit<Service, 'serviceId' | 'createTime'> = {
  serviceName: '',
  category: '',
  description: '',
  basePrice: 0,
  unit: '',
  duration: 60,
  status: 1,
};

const serviceForm = reactive({ ...initialFormState });

const rules = reactive<FormRules>({
  serviceName: [{ required: true, message: '请输入服务名称', trigger: 'blur' }],
  category: [{ required: true, message: '请输入服务类别', trigger: 'blur' }],
  basePrice: [{ required: true, message: '请输入基础价格', trigger: 'blur' }],
  unit: [{ required: true, message: '请输入计价单位', trigger: 'blur' }],
  duration: [{ required: true, message: '请输入标准时长', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
});

const handleAdd = () => {
  dialogMode.value = 'add';
  dialogTitle.value = '新增服务';
  Object.assign(serviceForm, initialFormState);
  serviceFormRef.value?.clearValidate();
  dialogVisible.value = true;
};

const handleEdit = (row: Service) => {
  dialogMode.value = 'edit';
  dialogTitle.value = '编辑服务';
  const rowData = JSON.parse(JSON.stringify(row));
  Object.assign(serviceForm, rowData);
  dialogVisible.value = true;
};

const handleDelete = (row: Service) => {
  ElMessageBox.confirm(`确定要删除服务 "${row.serviceName}" 吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    const index = serviceList.value.findIndex(s => s.serviceId === row.serviceId);
    if (index !== -1) {
      serviceList.value.splice(index, 1);
      ElMessage.success('删除成功');
      console.log('删除后，服务列表数据:', JSON.parse(JSON.stringify(serviceList.value)));
    }
  }).catch(() => {});
};

const formatDate = (date: Date) => {
  const Y = date.getFullYear();
  const M = (date.getMonth() + 1).toString().padStart(2, '0');
  const D = date.getDate().toString().padStart(2, '0');
  const h = date.getHours().toString().padStart(2, '0');
  const m = date.getMinutes().toString().padStart(2, '0');
  const s = date.getSeconds().toString().padStart(2, '0');
  return `${Y}-${M}-${D} ${h}:${m}:${s}`;
};

const submitForm = async () => {
  if (!serviceFormRef.value) return;
  await serviceFormRef.value.validate((valid) => {
    if (valid) {
      if (dialogMode.value === 'add') {
        const newService: Service = {
          serviceId: Math.max(...serviceList.value.map(s => s.serviceId), 0) + 1,
          ...serviceForm,
          createTime: formatDate(new Date()),
        };
        serviceList.value.unshift(newService);
        ElMessage.success('新增成功');
        console.log('新增后，服务列表数据:', JSON.parse(JSON.stringify(serviceList.value)));
      } else {
        const serviceIdToUpdate = (serviceForm as Service).serviceId;
        const index = serviceList.value.findIndex(s => s.serviceId === serviceIdToUpdate);
        if (index !== -1) {
          // Keep the original createTime
          serviceList.value[index] = { 
            ...serviceForm,
            serviceId: serviceList.value[index].serviceId,
            createTime: serviceList.value[index].createTime
          };
          ElMessage.success('更新成功');
          console.log('编辑后，服务列表数据:', JSON.parse(JSON.stringify(serviceList.value)));
        }
      }
      dialogVisible.value = false;
    }
  });
};

onMounted(() => {
  console.log("页面加载时，服务列表模拟数据:", JSON.parse(JSON.stringify(serviceList.value)));
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}
:deep(.el-card__body) {
  flex: 1;
  overflow: auto; 
}

</style> 