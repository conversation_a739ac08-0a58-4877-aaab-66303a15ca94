<template>
  <el-card class="page-card">
    <template #header>
      <div class="card-header">
        <span>公告管理</span>
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>
          发布公告
        </el-button>
      </div>
    </template>

    <el-table :data="noticeList" style="width: 100%" height="100%">
      <el-table-column prop="noticeId" label="ID" width="80" />
      <el-table-column prop="title" label="公告标题" width="300" show-overflow-tooltip />
      <el-table-column prop="noticeType" label="类型" width="120">
        <template #default="{ row }">
          <el-tag :type="row.noticeType === 1 ? 'info' : 'success'">
            {{ noticeTypeMap[row.noticeType] }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="publishTime" label="发布时间" width="180" />
      <el-table-column prop="expireTime" label="失效时间" width="180">
        <template #default="{ row }">
          {{ row.expireTime || '长期有效' }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-switch
            v-model="row.status"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="150">
        <template #default="{ row }">
          <el-button size="small" type="primary" @click="handleEdit(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(row.noticeId)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-card>

  <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px" @close="resetForm">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
      <el-form-item label="公告标题" prop="title">
        <el-input v-model="form.title" placeholder="请输入公告标题" />
      </el-form-item>
      <el-form-item label="公告类型" prop="noticeType">
        <el-radio-group v-model="form.noticeType">
          <el-radio :value="1">系统公告</el-radio>
          <el-radio :value="2">服务通知</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="失效时间" prop="expireTime">
        <el-date-picker
          v-model="form.expireTime"
          type="datetime"
          placeholder="留空表示长期有效"
          value-format="YYYY-MM-DD HH:mm:ss"
          clearable
        />
      </el-form-item>
      <el-form-item label="公告内容" prop="content">
        <el-input v-model="form.content" type="textarea" :rows="6" placeholder="请输入公告内容" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitForm">提交</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { mockNoticeList, type Notice } from '@/api/mockData/system';
import { mockAdminList } from '@/api/mockData/user';
import { Plus } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus';

const noticeList = ref<Notice[]>([]);
const dialogVisible = ref(false);
const dialogTitle = ref('');
const formRef = ref<FormInstance>();

const getInitialFormState = (): Notice => ({
  noticeId: 0,
  adminId: 0,
  title: '',
  content: '',
  noticeType: 1,
  publishTime: '',
  expireTime: null,
  status: 1,
});

let form = reactive<Notice>(getInitialFormState());

const rules = reactive<FormRules>({
  title: [{ required: true, message: '请输入公告标题', trigger: 'blur' }],
  noticeType: [{ required: true, message: '请选择公告类型', trigger: 'change' }],
  content: [{ required: true, message: '请输入公告内容', trigger: 'blur' }],
});

const noticeTypeMap: { [key: number]: string } = {
  1: '系统公告',
  2: '服务通知',
};

onMounted(() => {
  noticeList.value = JSON.parse(JSON.stringify(mockNoticeList));
});

const resetForm = () => {
  Object.assign(form, getInitialFormState());
  formRef.value?.clearValidate();
};

const handleCreate = () => {
  resetForm();
  dialogTitle.value = '发布新公告';
  dialogVisible.value = true;
};

const handleEdit = (row: Notice) => {
  Object.assign(form, row);
  dialogTitle.value = '编辑公告';
  dialogVisible.value = true;
};

const handleDelete = (noticeId: number) => {
  ElMessageBox.confirm('确定要删除此公告吗?', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    const index = noticeList.value.findIndex(n => n.noticeId === noticeId);
    if (index !== -1) {
      noticeList.value.splice(index, 1);
      ElMessage.success('删除成功');
    }
  });
};

const handleStatusChange = (row: Notice) => {
  const message = row.status === 1 ? '公告已上架' : '公告已下架';
  ElMessage.success(message);
};

const submitForm = async () => {
  if (!formRef.value) return;
  await formRef.value.validate((valid) => {
    if (valid) {
      if (form.noticeId) { // Edit
        const index = noticeList.value.findIndex(n => n.noticeId === form.noticeId);
        if (index !== -1) {
          noticeList.value[index] = { ...form };
          ElMessage.success('更新成功');
          console.log('公告更新成功，更新后数据:', JSON.parse(JSON.stringify(noticeList.value[index])));
        }
      } else { // Create
        const newNotice: Notice = {
          ...form,
          noticeId: Math.max(...noticeList.value.map(n => n.noticeId), 0) + 1,
          publishTime: new Date().toLocaleString('sv-SE').replace(' ', ' '),
          adminId: mockAdminList[0]?.adminId || 1, // Assign a default admin
        };
        noticeList.value.unshift(newNotice);
        ElMessage.success('发布成功');
        console.log('公告发布成功，新公告数据:', JSON.parse(JSON.stringify(newNotice)));
      }
      dialogVisible.value = false;
    }
  });
};
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}
:deep(.el-card__body) {
  flex: 1;
  overflow: hidden;
}
</style> 