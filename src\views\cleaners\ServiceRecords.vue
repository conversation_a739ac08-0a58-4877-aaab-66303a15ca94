<template>
  <div class="service-records-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>我的服务记录</span>
          <el-button type="primary" @click="openForm()">创建新记录</el-button>
        </div>
      </template>

      <div v-if="isLoading" class="loading-state">正在加载记录...</div>
      <div v-else-if="records.length === 0" class="empty-state">
        <el-empty description="暂无服务记录" />
      </div>

      <el-timeline v-else>
        <el-timeline-item
          v-for="record in records"
          :key="record.recordId"
          :timestamp="record.startTime"
          placement="top"
        >
          <el-card>
            <h4>预约ID: {{ record.bookingId }}</h4>
            <p><strong>服务时间:</strong> {{ record.startTime }} 至 {{ record.endTime }}</p>
            <p><strong>服务详情:</strong> {{ record.serviceDetails }}</p>
            <div v-if="record.photos && record.photos.length > 0" class="image-gallery">
                <strong>现场照片:</strong>
                <el-image
                    v-for="(photo, index) in record.photos"
                    :key="index"
                    style="width: 100px; height: 100px; margin-right: 10px;"
                    :src="photo"
                    :preview-src-list="record.photos"
                    :initial-index="index"
                    fit="cover"
                />
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </el-card>

    <ServiceRecordForm v-if="formVisible" @close="handleCloseForm" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import ServiceRecordForm from '../../components/cleaners/ServiceRecordForm.vue';
import type { ServiceRecord } from '../../types/record';

const records = ref<ServiceRecord[]>([]);
const isLoading = ref(true);
const formVisible = ref(false);

const fetchRecords = () => {
  isLoading.value = true;
  // 纯前端模拟
  setTimeout(() => {
    console.log("加载模拟服务记录...");
    records.value = [
      {
        recordId: 1,
        bookingId: 101,
        cleanerId: 1,
        startTime: '2024-06-18 09:00',
        endTime: '2024-06-18 11:00',
        serviceDetails: '完成了客厅和卧室的深度保洁，客户非常满意。',
        photos: [
          'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg',
          'https://fuss10.elemecdn.com/1/34/19aa98b1fcb2781c4fba33d850549jpeg.jpeg',
        ]
      },
      {
        recordId: 2,
        bookingId: 105,
        cleanerId: 1,
        startTime: '2024-06-17 14:00',
        endTime: '2024-06-17 16:00',
        serviceDetails: '厨房油烟机清洗完成。',
        photos: []
      }
    ];
    isLoading.value = false;
  }, 500);
};

const openForm = () => {
  formVisible.value = true;
};

const handleCloseForm = (refresh: boolean) => {
  formVisible.value = false;
  if (refresh) {
    // 刷新列表以显示新记录
    fetchRecords();
  }
};

onMounted(fetchRecords);
</script>

<style scoped>
.service-records-container {
  padding: 20px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.image-gallery {
    margin-top: 10px;
}
</style> 