<template>
  <div class="dashboard-container">
    <header class="dashboard-header">
      <h1>客户中心</h1>
      <button @click="handleLogout" class="logout-btn">退出登录</button>
    </header>
    <main class="dashboard-content">
      <p>欢迎您，{{ authStore.state.user?.username }}！</p>
      <p>在这里，您可以发布新的服务需求，并管理您的订单。</p>
    </main>
  </div>
</template>

<script setup lang="ts">
import { authStore } from '@/store/auth';
import { useRouter } from 'vue-router';

const router = useRouter();

/**
 * @description 处理退出登录逻辑
 */
const handleLogout = () => {
  authStore.clearAuth();
  router.push('/login');
};
</script> 