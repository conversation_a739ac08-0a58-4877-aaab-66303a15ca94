<template>
  <div class="training-center">
    <el-card>
       <template #header>
        <div class="card-header">
          <span>培训中心</span>
        </div>
      </template>

      <div v-if="isLoading" class="loading-state">正在加载培训列表...</div>
      <div v-else-if="error" class="error-state">{{ error }}</div>
      <div v-else-if="trainings.length === 0" class="empty-state">
        <p>当前没有可用的培训项目。</p>
      </div>

      <div v-else class="training-list">
        <el-card v-for="training in trainings" :key="training.id" class="training-card">
          <template #header>
            <div class="card-header">
              <span>{{ training.name }}</span>
              <el-tag :type="statusTagType(training.status)">{{ training.status }}</el-tag>
            </div>
          </template>
          <div class="training-content">
            <p>{{ training.content }}</p>
            <el-divider />
            <div class="training-details">
              <p><el-icon><Location /></el-icon> <strong>地点:</strong> {{ training.location }}</p>
              <p><el-icon><Calendar /></el-icon> <strong>时间:</strong> {{ training.startTime }} 到 {{ training.endTime }}</p>
              <p><el-icon><User /></el-icon> <strong>名额:</strong> {{ training.current_participants || 0 }} / {{ training.maxParticipants }} 人</p>
            </div>
          </div>
           <template #footer>
             <div v-if="training.status === 'PLANNED'">
                <el-button 
                    v-if="!isRegistered(training.id)"
                    type="primary" 
                    @click="handleRegister(training.id)">
                    报名参加
                </el-button>
                <el-button 
                    v-else
                    type="danger" 
                    plain
                    @click="handleUnregister(training.id)">
                    取消报名
                </el-button>
             </div>
             <div v-else>
                <el-button disabled>报名已截止</el-button>
             </div>
          </template>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { registerForTraining, unregisterFromTraining } from '../../api/training';
import type { Training } from '../../types/training';

const trainings = ref<Training[]>([]);
const isLoading = ref(true);
const error = ref<string | null>(null);

// 假设当前登录的家政人员ID为 1
const currentCleanerId = 1;
// 模拟用户已报名的培训ID列表
const registeredTrainingIds = ref<Array<string|number>>([2]);

const isRegistered = (trainingId: string | number) => {
    return registeredTrainingIds.value.includes(trainingId);
};

const statusTagType = (status: string) => {
  switch (status) {
    case 'PLANNED': return 'primary';
    case 'ONGOING': return 'success';
    case 'COMPLETED': return 'info';
    default: return 'info';
  }
};

const fetchTrainings = () => {
  isLoading.value = true;
  error.value = null;

  // 模拟网络请求延迟
  setTimeout(() => {
    console.log("正在加载纯前端模拟培训数据...");
    trainings.value = [
      { id: 1, name: '高级家电清洗技术培训', content: '深入学习空调、冰箱、洗衣机的专业清洗技巧。', startTime: '2024-08-01', endTime: '2024-08-02', location: '总部培训室A', maxParticipants: 20, current_participants: 5, status: 'PLANNED' },
      { id: 2, name: '客户沟通与礼仪培训', content: '提升服务过程中的沟通能力和职业礼仪。', startTime: '2024-07-20', endTime: '2024-07-20', location: '线上会议', maxParticipants: 50, current_participants: 49, status: 'PLANNED' },
      { id: 3, name: '春季大扫除专项技能', content: '学习针对春季大扫除的特殊清洁流程和工具使用。', startTime: '2024-03-15', endTime: '2024-03-16', location: '分部培训室', maxParticipants: 30, current_participants: 30, status: 'COMPLETED' },
      { id: 4, name: '夏季地毯深度护理', content: '学习各种材质地毯的深度清洁和保养方法。', startTime: '2024-06-10', endTime: '2024-06-11', location: '线上会议', maxParticipants: 40, current_participants: 40, status: 'ONGOING' },
    ];
    isLoading.value = false;
  }, 500); // 模拟500毫秒延迟
};

const handleRegister = async (trainingId: number | string) => {
  try {
    await registerForTraining(trainingId, currentCleanerId);
    ElMessage.success('报名成功（模拟）！');
    // 模拟更新UI
    registeredTrainingIds.value.push(trainingId);
    const training = trainings.value.find(t => t.id === trainingId);
    if (training) {
        training.current_participants = (training.current_participants || 0) + 1;
    }
  } catch (err: any) {
     if (err && !err.__MOCK__) {
        ElMessage.error('报名失败！');
     }
  }
};

const handleUnregister = async (trainingId: number | string) => {
  try {
    await unregisterFromTraining(trainingId, currentCleanerId);
    ElMessage.success('取消报名成功（模拟）！');
    // 模拟更新UI
    registeredTrainingIds.value = registeredTrainingIds.value.filter(id => id !== trainingId);
    const training = trainings.value.find(t => t.id === trainingId);
    if (training && training.current_participants) {
        training.current_participants -= 1;
    }
  } catch (err: any) {
     if (err && !err.__MOCK__) {
        ElMessage.error('取消报名失败！');
     }
  }
};

onMounted(fetchTrainings);
</script>

<style scoped>
.training-center {
  padding: 20px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.training-list {
  display: grid;
  gap: 20px;
}
.training-card {
  transition: box-shadow .3s;
}
.training-card:hover {
  box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
}
.training-details p {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 8px 0;
}
</style> 