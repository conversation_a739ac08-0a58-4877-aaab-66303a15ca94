<template>
  <div class="auth-container">
    <div class="logo">家政服务</div>
    <h2>注册</h2>
    <form @submit.prevent="handleRegister">
      <div class="form-group">
        <label for="username">用户名</label>
        <input type="text" id="username" v-model="form.username" required />
      </div>
      <div class="form-group">
        <label for="password">密码</label>
        <input type="password" id="password" v-model="form.password" required />
      </div>
       <div class="form-group">
        <label for="confirmPassword">确认密码</label>
        <input type="password" id="confirmPassword" v-model="confirmPassword" required />
      </div>
      <div class="form-group">
        <label for="phone">手机号</label>
        <input type="tel" id="phone" v-model="form.phone" required />
      </div>
      <div class="form-group">
        <label for="email">邮箱</label>
        <input type="email" id="email" v-model="form.email" required />
      </div>
      <div class="form-group">
        <label>用户类型</label>
        <div class="radio-group">
          <label>
            <input type="radio" :value="3" v-model.number="form.userType">
            客户
          </label>
          <label>
            <input type="radio" :value="2" v-model.number="form.userType">
            家政人员
          </label>
        </div>
      </div>
      <div class="form-group">
        <button type="submit">注册</button>
      </div>
    </form>
    <p>
      已有账户？ <router-link to="/login">立即登录</router-link>
    </p>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { register } from '@/api/auth';
import type { UserRegister } from '@/types/user';

const router = useRouter();
const form = reactive<UserRegister>({
  username: '',
  password: '',
  phone: '',
  email: '',
  userType: 3,
});
const confirmPassword = ref('');

/**
 * @description 处理注册逻辑
 */
const handleRegister = async () => {
  if (form.password !== confirmPassword.value) {
    alert('两次输入的密码不一致！');
    return;
  }
  try {
    await register(form);
    alert('注册成功！将跳转到登录页面。');
    router.push('/login');
  } catch (error: any) {
    console.error('Register failed', error);
    alert(`注册失败: ${error.message}`);
  }
};
</script>

<style scoped>
.radio-group label {
  margin-right: 15px;
  font-weight: normal;
  display: inline-flex;
  align-items: center;
}
.radio-group input {
    margin-right: 5px;
}
</style> 