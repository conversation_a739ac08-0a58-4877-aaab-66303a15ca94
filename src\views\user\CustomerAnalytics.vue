<template>
  <div class="analytics-container">
    <el-row :gutter="20">
      <!-- VIP Level Distribution -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>客户VIP等级分布</span>
            </div>
          </template>
          <v-chart class="chart" :option="vipChartOption" autoresize />
        </el-card>
      </el-col>

      <!-- Monthly New Customers -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>近6个月新增客户趋势</span>
            </div>
          </template>
          <v-chart class="chart" :option="monthlyChartOption" autoresize />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { PieChart, BarChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
} from 'echarts/components';
import VChart from 'vue-echarts';
import { mockVipLevelDistribution, mockMonthlyNewCustomers } from '@/api/mockData/analytics';

use([
  CanvasRenderer,
  PieChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
]);

const vipChartOption = ref({});
const monthlyChartOption = ref({});

onMounted(() => {
  // VIP Level Chart Option
  vipChartOption.value = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: mockVipLevelDistribution.map(item => item.name),
    },
    series: [
      {
        name: 'VIP等级',
        type: 'pie',
        radius: '70%',
        center: ['50%', '50%'],
        data: mockVipLevelDistribution,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  };

  // Monthly New Customers Chart Option
  monthlyChartOption.value = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: mockMonthlyNewCustomers.months,
        axisTick: {
          alignWithLabel: true,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
      },
    ],
    series: [
      {
        name: '新增客户数',
        type: 'bar',
        barWidth: '60%',
        data: mockMonthlyNewCustomers.counts,
      },
    ],
  };
});

</script>

<style scoped>
.analytics-container {
  padding: 20px;
}
.chart {
  height: 400px;
}
.card-header {
  font-weight: bold;
}
</style> 