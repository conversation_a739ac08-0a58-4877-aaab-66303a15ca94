<template>
  <el-card class="page-card">
    <template #header>
      <div class="card-header">
        <span>家政人员列表</span>
        <el-button class="button" type="primary" @click="handleAdd">新增家政人员</el-button>
      </div>
    </template>
    
    <el-table :data="cleanerList" style="width: 100%" height="100%">
      <el-table-column prop="cleanerId" label="ID" width="60" />
       <el-table-column label="头像" width="70">
        <template #default="scope">
          <el-avatar :src="scope.row.avatar" />
        </template>
      </el-table-column>
      <el-table-column prop="realName" label="姓名" width="90" />
      <el-table-column label="性别" width="60">
        <template #default="scope">{{ scope.row.gender === 0 ? '女' : '男' }}</template>
      </el-table-column>
      <el-table-column prop="age" label="年龄" width="60" />
      <el-table-column prop="education" label="学历" width="80" />
      <el-table-column prop="workYears" label="工龄" width="60" />
      <el-table-column prop="idCard" label="身份证号" width="180" />
      <el-table-column prop="skills" label="服务技能">
        <template #default="scope">
          <el-tag v-for="tag in JSON.parse(scope.row.skills)" :key="tag" style="margin: 2px;">{{ tag }}</el-tag>
        </template>
      </el-table-column>
       <el-table-column prop="qualification" label="资质证书">
        <template #default="scope">
          <el-tag v-for="tag in JSON.parse(scope.row.qualification)" :key="tag" type="success" style="margin: 2px;">{{ tag }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="rating" label="评分" width="160">
         <template #default="scope">
          <el-rate v-model="scope.row.rating" disabled />
        </template>
      </el-table-column>
      <el-table-column prop="status" label="工作状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status === 0 ? 'success' : 'warning'">
            {{ scope.row.status === 0 ? '空闲' : '忙碌' }}
          </el-tag>
        </template>
      </el-table-column>
       <el-table-column label="操作" width="150">
        <template #default="scope">
          <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-card>

  <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px" :close-on-click-modal="false">
    <el-form :model="cleanerForm" :rules="rules" ref="cleanerFormRef" label-width="80px">
      <el-form-item label="真实姓名" prop="realName">
        <el-input v-model="cleanerForm.realName" />
      </el-form-item>
      <el-form-item label="身份证号" prop="idCard">
        <el-input v-model="cleanerForm.idCard" />
      </el-form-item>
       <el-form-item label="性别" prop="gender">
        <el-radio-group v-model="cleanerForm.gender">
          <el-radio :label="0">女</el-radio>
          <el-radio :label="1">男</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="年龄" prop="age">
        <el-input-number v-model="cleanerForm.age" :min="18" :max="65" />
      </el-form-item>
      <el-form-item label="学历" prop="education">
        <el-select v-model="cleanerForm.education" placeholder="请选择学历">
          <el-option label="小学" value="小学"></el-option>
          <el-option label="初中" value="初中"></el-option>
          <el-option label="高中" value="高中"></el-option>
          <el-option label="大专" value="大专"></el-option>
          <el-option label="本科" value="本科"></el-option>
          <el-option label="硕士及以上" value="硕士及以上"></el-option>
        </el-select>
      </el-form-item>
       <el-form-item label="工作年限" prop="workYears">
        <el-input-number v-model="cleanerForm.workYears" :min="0" />
      </el-form-item>
      <el-form-item label="服务技能" prop="skills">
        <el-select v-model="cleanerForm.skills" multiple placeholder="请选择技能">
          <el-option label="日常保洁" value="日常保洁"></el-option>
          <el-option label="家电清洗" value="家电清洗"></el-option>
          <el-option label="家电维修" value="家电维修"></el-option>
          <el-option label="育儿陪护" value="育儿陪护"></el-option>
          <el-option label="家庭烹饪" value="家庭烹饪"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="上传证书" prop="qualification">
        <el-upload
          v-model:file-list="certFileList"
          action="#"
          list-type="picture-card"
          :before-upload="beforeCertUpload"
          :on-preview="handlePictureCardPreview"
          :http-request="() => {}"
          multiple
        >
          <el-icon><Plus /></el-icon>
        </el-upload>
      </el-form-item>
      <el-form-item label="工作状态" prop="status">
        <el-radio-group v-model="cleanerForm.status">
          <el-radio :label="0">空闲</el-radio>
          <el-radio :label="1">忙碌</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="头像" prop="avatar">
        <el-upload
          class="avatar-uploader"
          action="#"
          :show-file-list="false"
          :before-upload="beforeAvatarUpload"
          :http-request="() => {}"
        >
          <img v-if="cleanerForm.avatar" :src="cleanerForm.avatar" class="avatar" />
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
        </el-upload>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </span>
    </template>
  </el-dialog>

  <el-dialog v-model="previewDialogVisible">
    <img :src="dialogImageUrl" alt="Preview Image" style="width: 100%" />
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive, watch } from 'vue'
import { mockCleanerList } from '@/api/mockData/user'
import type { FormInstance, FormRules, UploadProps, UploadUserFile, UploadFile } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

interface Cleaner {
  cleanerId: number;
  userId: number;
  realName: string;
  idCard: string;
  gender: number;
  age: number;
  education: string;
  workYears: number;
  qualification: string; // JSON string
  skills: string; // JSON string
  status: number;
  rating: number;
  avatar: string;
}

// Form data type, with skills and qualification as array for easier handling in form
type CleanerFormData = Omit<Cleaner, 'qualification' | 'skills'> & {
  qualification: string[];
  skills: string[];
}

const cleanerList = ref<Cleaner[]>(mockCleanerList)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const dialogMode = ref<'add' | 'edit'>('add')
const cleanerFormRef = ref<FormInstance>()
const certFileList = ref<UploadUserFile[]>([])
const dialogImageUrl = ref('')
const previewDialogVisible = ref(false)

const initialFormState: CleanerFormData = {
  cleanerId: 0,
  userId: 0,
  realName: '',
  idCard: '',
  gender: 0,
  age: 18,
  education: '',
  workYears: 0,
  qualification: [],
  skills: [],
  status: 0,
  rating: 0,
  avatar: '',
};
const cleanerForm = reactive<CleanerFormData>({ ...initialFormState });

const rules = reactive<FormRules>({
  realName: [{ required: true, message: '请输入真实姓名', trigger: 'blur' }],
  idCard: [{ required: true, message: '请输入身份证号', trigger: 'blur' }],
  gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
  age: [{ required: true, message: '请输入年龄', trigger: 'blur' }],
  education: [{ required: true, message: '请选择学历', trigger: 'change' }],
  workYears: [{ required: true, message: '请输入工作年限', trigger: 'blur' }],
  skills: [{ required: true, message: '请选择服务技能', trigger: 'change' }],
  status: [{ required: true, message: '请选择工作状态', trigger: 'change' }],
  avatar: [{ required: true, message: '请上传头像', trigger: 'change' }],
})

watch(certFileList, (newFileList) => {
  cleanerForm.qualification = newFileList.map(file => file.url || '')
})

const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
  const isJpgOrPng = rawFile.type === 'image/jpeg' || rawFile.type === 'image/png';
  if (!isJpgOrPng) {
    ElMessage.error('头像只能是 JPG/PNG 格式!');
    return false;
  } else if (rawFile.size / 1024 / 1024 > 2) {
    ElMessage.error('头像大小不能超过 2MB!');
    return false;
  }
  cleanerForm.avatar = URL.createObjectURL(rawFile);
  return false;
}

const beforeCertUpload: UploadProps['beforeUpload'] = (rawFile) => {
  const isJpgOrPng = rawFile.type === 'image/jpeg' || rawFile.type === 'image/png';
  if (!isJpgOrPng) {
    ElMessage.error('证书只能是 JPG/PNG 格式!');
    return false;
  } else if (rawFile.size / 1024 / 1024 > 2) {
    ElMessage.error('证书大小不能超过 2MB!');
    return false;
  }
  return true;
}

const handlePictureCardPreview = (uploadFile: UploadFile) => {
  dialogImageUrl.value = uploadFile.url!
  previewDialogVisible.value = true
}

const handleAdd = () => {
  dialogMode.value = 'add';
  dialogTitle.value = '新增家政人员';
  Object.assign(cleanerForm, initialFormState); // Reset form
  certFileList.value = []
  cleanerFormRef.value?.clearValidate();
  dialogVisible.value = true;
};

const handleEdit = (row: Cleaner) => {
  dialogMode.value = 'edit';
  dialogTitle.value = '编辑家政人员';
  
  // Deep copy and parse JSON strings for form
  const rowData = JSON.parse(JSON.stringify(row))
  Object.assign(cleanerForm, {
    ...rowData,
    skills: JSON.parse(rowData.skills || '[]'),
    qualification: JSON.parse(rowData.qualification || '[]'),
  });
  
  certFileList.value = (cleanerForm.qualification || []).map(url => ({ name: url, url }))

  dialogVisible.value = true;
};

const handleDelete = (row: Cleaner) => {
  ElMessageBox.confirm(`确定要删除家政人员 "${row.realName}" 吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    const index = cleanerList.value.findIndex(c => c.cleanerId === row.cleanerId);
    if (index !== -1) {
      cleanerList.value.splice(index, 1);
      ElMessage.success('删除成功');
      console.log('删除后，家政人员列表数据:', JSON.parse(JSON.stringify(cleanerList.value)));
    }
  }).catch(() => {});
};

const submitForm = async () => {
  if (!cleanerFormRef.value) return;
  await cleanerFormRef.value.validate((valid) => {
    if (valid) {
      const dataToSave = {
        ...cleanerForm,
        skills: JSON.stringify(cleanerForm.skills),
        qualification: JSON.stringify(cleanerForm.qualification),
      };

      if (dialogMode.value === 'add') {
        const newCleaner: Cleaner = {
          ...dataToSave,
          cleanerId: Math.max(...cleanerList.value.map(c => c.cleanerId)) + 1,
          userId: Math.max(...cleanerList.value.map(c => c.userId)) + 1,
          rating: 0, // New cleaners start with 0 rating
        };
        cleanerList.value.unshift(newCleaner);
        ElMessage.success('新增成功');
        console.log('新增后，家政人员列表数据:', JSON.parse(JSON.stringify(cleanerList.value)));
      } else {
        const index = cleanerList.value.findIndex(c => c.cleanerId === dataToSave.cleanerId);
        if (index !== -1) {
          cleanerList.value[index] = dataToSave;
          ElMessage.success('更新成功');
          console.log('编辑后，家政人员列表数据:', JSON.parse(JSON.stringify(cleanerList.value)));
        }
      }
      dialogVisible.value = false;
    }
  });
};

onMounted(() => {
  console.log("页面加载时，家政人员列表模拟数据:", JSON.parse(JSON.stringify(cleanerList.value)));
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}
:deep(.el-card__body) {
  flex: 1;
  overflow: hidden;
}
.el-rate {
  --el-rate-disabled-void-color: var(--el-text-color-placeholder);
}
.avatar-uploader .avatar {
  width: 120px;
  height: 120px;
  display: block;
}
</style>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  text-align: center;
}
</style> 