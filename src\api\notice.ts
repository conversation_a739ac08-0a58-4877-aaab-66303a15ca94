import service from './index';
import type { NoticeQueryParams } from '../types/notice';

/**
 * @description 获取公告列表
 * @param params 查询参数
 */
export const getNotices = (params?: NoticeQueryParams): Promise<any> => {
  return service({
    url: '/notices',
    method: 'get',
    params,
  });
};

/**
 * @description 获取公告详情
 * @param noticeId 公告ID
 */
export const getNoticeDetails = (noticeId: number | string): Promise<any> => {
  return service({
    url: `/notices/${noticeId}`,
    method: 'get',
  });
}; 