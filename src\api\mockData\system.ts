export interface KeyMetrics {
  totalRevenue: number;
  totalOrders: number;
  newCustomers: number;
  satisfactionRate: number;
}

export interface ChartData {
  dates: string[];
  revenue: number[];
  orders: number[];
}

export interface ServiceDistribution {
  name: string;
  value: number;
}

export interface Statistic {
  statsId: number;
  statsType: string;
  statsPeriod: '日' | '周' | '月' | '年' | '总览';
  statsDate: string; 
  data: string; // JSON string
  createTime: string;
}

export interface Notice {
  noticeId: number;
  adminId: number;
  title: string;
  content: string;
  noticeType: number; // 1=系统公告, 2=服务通知
  publishTime: string;
  expireTime: string | null;
  status: number; // 0=已下架, 1=上架
}

// --- 模拟数据 ---

export const mockKeyMetrics: KeyMetrics = {
  totalRevenue: 185600.50,
  totalOrders: 1240,
  newCustomers: 215,
  satisfactionRate: 98.2,
};

export const mockWeeklyChartData: ChartData = {
  dates: ['2024-06-13', '2024-06-14', '2024-06-15', '2024-06-16', '2024-06-17', '2024-06-18', '2024-06-19'],
  revenue: [1200, 1500, 2100, 1800, 2500, 2300, 2800],
  orders: [30, 35, 45, 40, 55, 52, 60],
};

export const mockServiceDistribution: ServiceDistribution[] = [
    { value: 450, name: '日常保洁' },
    { value: 310, name: '深度除螨' },
    { value: 235, name: '家电清洗' },
    { value: 180, name: '金牌月嫂' },
    { value: 165, name: '家庭收纳' },
];

export const mockStatisticsList: Statistic[] = [
  {
    statsId: 1,
    statsType: '整体KPI',
    statsPeriod: '总览',
    statsDate: '2024-06-19',
    data: JSON.stringify({
      totalRevenue: 185600.50,
      totalOrders: 1240,
      newCustomers: 215,
      satisfactionRate: 98.2,
    }),
    createTime: '2024-06-20 01:00:00',
  },
  {
    statsId: 2,
    statsType: '订单周报',
    statsPeriod: '周',
    statsDate: '2024-06-19',
    data: JSON.stringify({
      dates: ['2024-06-13', '2024-06-14', '2024-06-15', '2024-06-16', '2024-06-17', '2024-06-18', '2024-06-19'],
      revenue: [1200, 1500, 2100, 1800, 2500, 2300, 2800],
      orders: [30, 35, 45, 40, 55, 52, 60],
    }),
    createTime: '2024-06-20 01:00:00',
  },
  {
    statsId: 3,
    statsType: '服务分布',
    statsPeriod: '总览',
    statsDate: '2024-06-19',
    data: JSON.stringify([
      { value: 450, name: '日常保洁' },
      { value: 310, name: '深度除螨' },
      { value: 235, name: '家电清洗' },
      { value: 180, name: '金牌月嫂' },
      { value: 165, name: '家庭收纳' },
    ]),
    createTime: '2024-06-20 01:00:00',
  },
    {
    statsId: 4,
    statsType: '订单',
    statsPeriod: '日',
    statsDate: '2024-06-18',
    data: JSON.stringify({ count: 52, total_amount: 2300.00 }),
    createTime: '2024-06-19 02:00:00',
  },
  {
    statsId: 5,
    statsType: '客户',
    statsPeriod: '月',
    statsDate: '2024-05-31',
    data: JSON.stringify({ new_users: 88, active_users: 540 }),
    createTime: '2024-06-01 02:00:00',
  },
    {
    statsId: 6,
    statsType: '人员',
    statsPeriod: '周',
    statsDate: '2024-06-17',
    data: JSON.stringify({ new_cleaners: 5, active_cleaners: 120 }),
    createTime: '2024-06-18 03:00:00',
  },
  {
    statsId: 7,
    statsType: '服务项目',
    statsPeriod: '月',
    statsDate: '2024-06-30',
    data: JSON.stringify({
      most_booked_service: {
        service_id: 1,
        service_name: '日常保洁套餐',
        category: '保洁',
        booking_count: 152
      },
      least_booked_service: {
        service_id: 5,
        service_name: '及婷',
        category: 'Lorem',
        booking_count: 3
      }
    }),
    createTime: '2024-07-01 04:00:00',
  }
];

export const mockNoticeList: Notice[] = [
  {
    noticeId: 1,
    adminId: 1,
    title: '关于五一劳动节放假安排的通知',
    content: '根据国家法定节假日规定，结合公司实际情况，现将2024年五一劳动节放假安排通知如下：5月1日至5月5日放假调休，共5天。4月28日（星期日）、5月11日（星期六）上班。请各位员工提前安排好工作生活，节日期间注意安全，度过一个欢乐、祥和的节日假期。',
    noticeType: 1, // 系统公告
    publishTime: '2024-04-25 10:00:00',
    expireTime: '2024-05-06 00:00:00',
    status: 1, // 上架
  },
  {
    noticeId: 2,
    adminId: 2,
    title: '夏季深度保洁服务上线通知',
    content: '尊敬的用户，为了迎接夏季的到来，我们特别推出了"夏季深度保洁套餐"，包含空调深度清洗、全屋除螨等项目，即日起可通过App或小程序预约。',
    noticeType: 2, // 服务通知
    publishTime: '2024-06-01 09:00:00',
    expireTime: null,
    status: 1, // 上架
  },
  {
    noticeId: 3,
    adminId: 1,
    title: '系统将于本周六凌晨进行升级维护',
    content: '为了提供更优质的服务，我们的系统将于本周六（6月22日）凌晨2:00-4:00进行升级维护，届时预约功能将暂停使用，给您带来的不便敬请谅解。',
    noticeType: 1, // 系统公告
    publishTime: '2024-06-19 18:00:00',
    expireTime: '2024-06-22 04:00:00',
    status: 0, // 已下架
  },
]; 