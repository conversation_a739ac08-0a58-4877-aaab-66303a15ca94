<template>
  <el-container class="cleaner-dashboard">
    <el-aside width="200px">
      <div class="aside-header">
        <h2>家政人员</h2>
      </div>
      <el-menu
        router
        :default-active="$route.path"
        background-color="#2d3a4b"
        text-color="#bfcbd9"
        active-text-color="#409EFF">
        <el-menu-item index="/cleaner/tasks">
          <el-icon><List /></el-icon>
          <span>我的任务</span>
        </el-menu-item>
        <el-menu-item index="/cleaner/profile">
          <el-icon><User /></el-icon>
          <span>个人资料</span>
        </el-menu-item>
        <el-menu-item index="/cleaner/training">
          <el-icon><School /></el-icon>
          <span>培训中心</span>
        </el-menu-item>
        <el-menu-item index="/cleaner/records">
          <el-icon><Document /></el-icon>
          <span>服务记录</span>
        </el-menu-item>
        <el-menu-item index="/cleaner/feedback">
          <el-icon><ChatDotSquare /></el-icon>
          <span>客户评价</span>
        </el-menu-item>
        <el-menu-item index="/cleaner/notices">
          <el-icon><Bell /></el-icon>
          <span>公司公告</span>
        </el-menu-item>
        <el-menu-item index="/cleaner/report">
          <el-icon><DataLine /></el-icon>
          <span>工作报告</span>
        </el-menu-item>
      </el-menu>
    </el-aside>
    <el-container>
      <el-header class="main-header">
        <div class="header-left">
          <!-- 面包屑导航或其他 -->
        </div>
        <div class="header-right">
          <el-button type="primary" @click="logout">退出登录</el-button>
        </div>
      </el-header>
      <el-main>
        <router-view></router-view>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';

const router = useRouter();

const logout = () => {
  console.log('执行退出登录操作');
  router.push('/login');
};
</script>

<style scoped>
.cleaner-dashboard {
  height: 100vh;
}
.el-aside {
  background-color: #2d3a4b;
  color: #fff;
  display: flex;
  flex-direction: column;
}
.aside-header {
  padding: 20px;
  text-align: center;
}
.logo {
  width: 32px;
  height: 32px;
}
.el-menu {
  border-right: none;
}
.el-menu-item:hover {
  background-color: #263445 !important;
}
.main-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  border-bottom: 1px solid #dcdfe6;
}
.el-main {
  background-color: #f0f2f5;
}
</style> 