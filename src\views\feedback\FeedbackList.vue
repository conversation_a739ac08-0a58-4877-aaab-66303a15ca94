<template>
  <el-card class="page-card">
    <template #header>
      <div class="card-header">
        <span>评价列表</span>
      </div>
    </template>

    <el-table :data="feedbackTableData" style="width: 100%" height="100%">
      <el-table-column prop="feedbackId" label="ID" width="60" />
      <el-table-column prop="bookingId" label="关联预定ID" width="110" />
      <el-table-column prop="customerName" label="客户" width="120" />
      <el-table-column prop="cleanerName" label="家政员" width="120" />
      <el-table-column prop="rating" label="评分" width="180">
        <template #default="scope">
          <el-rate v-model="scope.row.rating" disabled />
        </template>
      </el-table-column>
      <el-table-column prop="feedbackText" label="评价内容" show-overflow-tooltip />
      <el-table-column prop="feedbackTime" label="评价时间" width="180" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
            {{ scope.row.status === 1 ? '已回复' : '待回复' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="adminReply" label="管理员回复" show-overflow-tooltip>
         <template #default="scope">
          {{ scope.row.adminReply || '-' }}
        </template>
      </el-table-column>
       <el-table-column prop="replyTime" label="回复时间" width="180">
         <template #default="scope">
          {{ scope.row.replyTime || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100" fixed="right">
        <template #default="scope">
          <el-button size="small" type="primary" @click="handleReply(scope.row)">
            {{ scope.row.status === 1 ? '编辑回复' : '回复' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-card>

  <el-dialog v-model="dialogVisible" title="回复评价" width="500px">
    <el-form :model="replyForm" ref="replyFormRef" label-width="80px">
       <el-form-item label="评价内容">
         <el-input v-model="currentFeedbackText" type="textarea" :rows="4" disabled />
       </el-form-item>
       <el-form-item label="回复内容" prop="adminReply">
        <el-input v-model="replyForm.adminReply" type="textarea" :rows="4" placeholder="请输入回复内容"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitReply">提交</el-button>
      </span>
    </template>
  </el-dialog>

</template>

<script lang="ts" setup>
import { ref, onMounted, reactive, computed } from 'vue';
import { mockFeedbackList, type Feedback } from '@/api/mockData/feedback';
import { mockCustomerList } from '@/api/mockData/user';
import { mockCleanerList } from '@/api/mockData/user';
import type { FormInstance } from 'element-plus';
import { ElMessage } from 'element-plus';

const feedbackList = ref<Feedback[]>(JSON.parse(JSON.stringify(mockFeedbackList)));
const dialogVisible = ref(false);
const replyFormRef = ref<FormInstance>();
const replyForm = reactive({
  feedbackId: null,
  adminReply: '',
});
const currentFeedbackText = ref('');

const customerNameMap = computed(() => {
  const map = new Map<number, string>();
  mockCustomerList.forEach(c => map.set(c.customerId, c.realName));
  return map;
});

const cleanerNameMap = computed(() => {
  const map = new Map<number, string>();
  mockCleanerList.forEach(c => map.set(c.cleanerId, c.realName));
  return map;
});

const feedbackTableData = computed(() => {
  return feedbackList.value.map(f => ({
    ...f,
    customerName: customerNameMap.value.get(f.customerId) || `客户ID:${f.customerId}`,
    cleanerName: cleanerNameMap.value.get(f.cleanerId) || `家政员ID:${f.cleanerId}`,
  }));
});

const handleReply = (row: any) => {
  replyForm.feedbackId = row.feedbackId;
  replyForm.adminReply = row.adminReply || '';
  currentFeedbackText.value = row.feedbackText;
  dialogVisible.value = true;
};

const formatDate = (date: Date) => {
  const Y = date.getFullYear();
  const M = (date.getMonth() + 1).toString().padStart(2, '0');
  const D = date.getDate().toString().padStart(2, '0');
  const h = date.getHours().toString().padStart(2, '0');
  const m = date.getMinutes().toString().padStart(2, '0');
  const s = date.getSeconds().toString().padStart(2, '0');
  return `${Y}-${M}-${D} ${h}:${m}:${s}`;
};

const submitReply = async () => {
  if (!replyFormRef.value) return;
  await replyFormRef.value.validate((valid) => {
    if(valid) {
      const index = feedbackList.value.findIndex(f => f.feedbackId === replyForm.feedbackId);
      if(index !== -1) {
        feedbackList.value[index].adminReply = replyForm.adminReply;
        feedbackList.value[index].status = 1;
        feedbackList.value[index].replyTime = formatDate(new Date());
        ElMessage.success('回复成功');
        console.log('回复后，评价列表数据:', JSON.parse(JSON.stringify(feedbackList.value)));
      }
      dialogVisible.value = false;
    }
  });
};

onMounted(() => {
  console.log('页面加载时，评价列表数据:', JSON.parse(JSON.stringify(feedbackList.value)));
});

</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}
:deep(.el-card__body) {
  flex: 1;
  overflow: hidden;
}
</style> 