<template>
  <div class="cleaner-training-page">
    <div class="page-header">
      <h1 class="page-title">培训中心</h1>
      <p class="page-subtitle">提升专业技能，获得更多机会</p>
    </div>

    <!-- 培训统计 -->
    <div class="training-stats">
      <div class="stat-card">
        <el-icon class="stat-icon"><Reading /></el-icon>
        <div class="stat-content">
          <div class="stat-number">{{ completedCourses }}</div>
          <div class="stat-label">已完成课程</div>
        </div>
      </div>
      <div class="stat-card">
        <el-icon class="stat-icon"><Trophy /></el-icon>
        <div class="stat-content">
          <div class="stat-number">{{ certificates }}</div>
          <div class="stat-label">获得证书</div>
        </div>
      </div>
      <div class="stat-card">
        <el-icon class="stat-icon"><Clock /></el-icon>
        <div class="stat-content">
          <div class="stat-number">{{ studyHours }}</div>
          <div class="stat-label">学习时长(小时)</div>
        </div>
      </div>
    </div>

    <!-- 课程列表 -->
    <div class="course-section">
      <h2 class="section-title">推荐课程</h2>
      <div class="course-grid">
        <div v-for="course in courses" :key="course.id" class="course-card">
          <div class="course-image">
            <img :src="course.image" :alt="course.title" />
            <div class="course-duration">{{ course.duration }}</div>
          </div>
          <div class="course-content">
            <h3 class="course-title">{{ course.title }}</h3>
            <p class="course-description">{{ course.description }}</p>
            <div class="course-meta">
              <span class="course-level">{{ course.level }}</span>
              <span class="course-students">{{ course.students }}人学习</span>
            </div>
            <div class="course-actions">
              <el-button 
                v-if="!course.enrolled" 
                type="primary" 
                @click="enrollCourse(course)"
              >
                立即报名
              </el-button>
              <el-button 
                v-else 
                type="success" 
                @click="continueCourse(course)"
              >
                继续学习
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 我的证书 -->
    <div class="certificate-section">
      <h2 class="section-title">我的证书</h2>
      <div class="certificate-grid">
        <div v-for="cert in myCertificates" :key="cert.id" class="certificate-card">
          <div class="certificate-icon">
            <el-icon><Medal /></el-icon>
          </div>
          <div class="certificate-info">
            <h3>{{ cert.name }}</h3>
            <p>获得时间：{{ cert.date }}</p>
            <p>有效期：{{ cert.validity }}</p>
          </div>
          <el-button text type="primary" @click="viewCertificate(cert)">
            查看证书
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import { Reading, Trophy, Clock, Medal } from '@element-plus/icons-vue';

const completedCourses = ref(8);
const certificates = ref(3);
const studyHours = ref(24);

const courses = ref([
  {
    id: 1,
    title: '家庭清洁基础技能',
    description: '学习基本的家庭清洁技巧和工具使用方法',
    image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300',
    duration: '2小时',
    level: '初级',
    students: 1234,
    enrolled: false
  },
  {
    id: 2,
    title: '高级清洁技术',
    description: '掌握专业的深度清洁和特殊材质清洁技术',
    image: 'https://images.unsplash.com/photo-1527515637462-cff94eecc1ac?w=300',
    duration: '3小时',
    level: '高级',
    students: 856,
    enrolled: true
  },
  {
    id: 3,
    title: '客户服务技巧',
    description: '提升与客户沟通的技巧，提高服务满意度',
    image: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=300',
    duration: '1.5小时',
    level: '中级',
    students: 967,
    enrolled: false
  }
]);

const myCertificates = ref([
  {
    id: 1,
    name: '家政服务基础证书',
    date: '2023-06-15',
    validity: '长期有效'
  },
  {
    id: 2,
    name: '高级清洁技术证书',
    date: '2023-09-20',
    validity: '2026-09-20'
  },
  {
    id: 3,
    name: '客户服务优秀证书',
    date: '2023-12-10',
    validity: '2025-12-10'
  }
]);

const enrollCourse = (course: any) => {
  course.enrolled = true;
  ElMessage.success(`成功报名课程：${course.title}`);
};

const continueCourse = (course: any) => {
  ElMessage.info(`继续学习：${course.title}`);
};

const viewCertificate = (cert: any) => {
  ElMessage.info(`查看证书：${cert.name}`);
};
</script>

<style scoped>
.cleaner-training-page {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 32px;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #6b7280;
  margin: 0;
}

.training-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  font-size: 32px;
  color: #4f46e5;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 20px 0;
}

.course-section {
  margin-bottom: 40px;
}

.course-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.course-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease;
}

.course-card:hover {
  transform: translateY(-4px);
}

.course-image {
  position: relative;
  height: 160px;
  overflow: hidden;
}

.course-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.course-duration {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.course-content {
  padding: 20px;
}

.course-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.course-description {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 16px 0;
  line-height: 1.5;
}

.course-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  font-size: 12px;
  color: #9ca3af;
}

.certificate-section {
  margin-bottom: 40px;
}

.certificate-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.certificate-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.certificate-icon {
  width: 48px;
  height: 48px;
  background: #fef3c7;
  color: #d97706;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.certificate-info {
  flex: 1;
}

.certificate-info h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.certificate-info p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}
</style>
