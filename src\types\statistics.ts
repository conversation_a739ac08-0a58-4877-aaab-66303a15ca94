/**
 * @description 个人工作报告的统计数据结构
 */
export interface WorkReportData {
  totalBookings: number; // 总订单数
  totalHours: number; // 总工时
  averageRating: number; // 平均评分
  serviceTypeDistribution: Array<{
    name: string; // 服务类型名称
    value: number; // 该类型的订单数
  }>;
  recentBookings: Array<{
    id: string | number;
    customerName: string;
    serviceDate: string;
    status: string;
  }>;
}

/**
 * @description 获取统计数据的查询参数
 */
export interface StatisticsQueryParams {
  statsType: 'cleaner_report'; // 统计类型
  statsPeriod?: 'monthly' | 'weekly' | 'custom';
  // 如果是 custom, 需要提供 startDate 和 endDate
  startDate?: string;
  endDate?: string;
  cleanerId?: number; // 指定家政人员ID
} 