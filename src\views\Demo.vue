<template>
  <PageLayout title="UI组件演示" subtitle="展示优化后的界面组件和设计系统">
    <template #actions>
      <button class="btn btn-primary">
        <svg class="btn-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 5V19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M5 12H19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        新建
      </button>
    </template>

    <div class="demo-container">
      <!-- 按钮演示 -->
      <div class="demo-section">
        <h3 class="section-title">按钮组件</h3>
        <div class="demo-grid">
          <button class="btn btn-primary">主要按钮</button>
          <button class="btn btn-secondary">次要按钮</button>
          <button class="btn btn-success">成功按钮</button>
          <button class="btn btn-warning">警告按钮</button>
          <button class="btn btn-danger">危险按钮</button>
          <button class="btn btn-primary" disabled>禁用按钮</button>
        </div>
      </div>

      <!-- 卡片演示 -->
      <div class="demo-section">
        <h3 class="section-title">卡片组件</h3>
        <div class="grid grid-cols-3 gap-4">
          <div class="card hover-lift">
            <div class="card-header">
              <h4 class="card-title">卡片标题</h4>
            </div>
            <div class="card-body">
              <p>这是一个示例卡片，展示了现代化的设计风格和悬停效果。</p>
            </div>
            <div class="card-footer">
              <button class="btn btn-sm btn-primary">查看详情</button>
            </div>
          </div>

          <div class="card hover-lift">
            <div class="card-header">
              <h4 class="card-title">统计数据</h4>
            </div>
            <div class="card-body">
              <div class="stat-item">
                <span class="stat-number">1,234</span>
                <span class="stat-label">总任务数</span>
              </div>
            </div>
          </div>

          <div class="card hover-lift">
            <div class="card-header">
              <h4 class="card-title">快速操作</h4>
            </div>
            <div class="card-body">
              <div class="flex flex-col gap-2">
                <button class="btn btn-sm btn-secondary">操作一</button>
                <button class="btn btn-sm btn-secondary">操作二</button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 徽章演示 -->
      <div class="demo-section">
        <h3 class="section-title">徽章组件</h3>
        <div class="demo-grid">
          <span class="badge badge-primary">主要</span>
          <span class="badge badge-success">成功</span>
          <span class="badge badge-warning">警告</span>
          <span class="badge badge-danger">危险</span>
          <span class="badge badge-info">信息</span>
        </div>
      </div>

      <!-- 表单演示 -->
      <div class="demo-section">
        <h3 class="section-title">表单组件</h3>
        <div class="grid grid-cols-2 gap-6">
          <div class="form-group">
            <label class="form-label">用户名</label>
            <input type="text" class="form-input" placeholder="请输入用户名" />
          </div>
          <div class="form-group">
            <label class="form-label">邮箱</label>
            <input type="email" class="form-input" placeholder="请输入邮箱" />
          </div>
          <div class="form-group">
            <label class="form-label">描述</label>
            <textarea class="form-textarea" rows="3" placeholder="请输入描述"></textarea>
          </div>
          <div class="form-group">
            <label class="form-label">状态</label>
            <select class="form-select">
              <option>请选择状态</option>
              <option>活跃</option>
              <option>非活跃</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 提示消息演示 -->
      <div class="demo-section">
        <h3 class="section-title">提示消息</h3>
        <div class="flex flex-col gap-4">
          <AlertMessage type="success" message="操作成功！数据已保存。" />
          <AlertMessage type="warning" message="请注意：此操作不可撤销。" />
          <AlertMessage type="error" message="操作失败：网络连接异常。" />
          <AlertMessage type="info" message="提示：您有新的消息待查看。" />
        </div>
      </div>

      <!-- 加载状态演示 -->
      <div class="demo-section">
        <h3 class="section-title">加载状态</h3>
        <div class="flex items-center gap-8">
          <LoadingSpinner size="sm" text="小尺寸" />
          <LoadingSpinner size="md" text="中等尺寸" />
          <LoadingSpinner size="lg" text="大尺寸" />
        </div>
      </div>

      <!-- 动画效果演示 -->
      <div class="demo-section">
        <h3 class="section-title">动画效果</h3>
        <div class="demo-grid">
          <div class="card hover-lift animate-fade-in">
            <div class="card-body text-center">
              <p>悬停提升效果</p>
            </div>
          </div>
          <div class="card hover-scale animate-slide-in-left">
            <div class="card-body text-center">
              <p>悬停缩放效果</p>
            </div>
          </div>
          <div class="card hover-glow animate-slide-in-right">
            <div class="card-body text-center">
              <p>悬停发光效果</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PageLayout>
</template>

<script setup lang="ts">
import PageLayout from '../components/PageLayout.vue';
import AlertMessage from '../components/AlertMessage.vue';
import LoadingSpinner from '../components/LoadingSpinner.vue';
</script>

<style scoped>
.demo-container {
  max-width: 1000px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: var(--spacing-12);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-6);
  padding-bottom: var(--spacing-3);
  border-bottom: 2px solid var(--primary-color);
}

.demo-grid {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-4);
  align-items: center;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--spacing-1);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .demo-grid {
    flex-direction: column;
    align-items: stretch;
  }
  
  .grid-cols-3 {
    grid-template-columns: 1fr;
  }
  
  .grid-cols-2 {
    grid-template-columns: 1fr;
  }
}
</style>
