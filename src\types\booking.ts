/**
 * @description 预约任务信息
 */
export interface Booking {
  bookingId: number | string;
  customerName: string; // 假设后端会提供客户姓名
  serviceName: string;  // 假设后端会提供服务名称
  serviceDate: string;  // e.g., "2025-06-18"
  serviceTime: string;  // e.g., "14:00"
  address: string;
  status: number; // 0:待服务,,1:已分配 2:服务中, 3:已完成, 4:已取消
  customerPhone?: string;
  remarks?: string;
  bookingTime: string;
}

/**
 * @description 获取预约列表的查询参数
 */
export interface BookingQueryParams {
  status?: number;
  serviceDate?: string;
} 