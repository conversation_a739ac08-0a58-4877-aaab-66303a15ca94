export interface Training {
  id: number;
  name: string;
  content: string;
  startTime: string;
  endTime: string;
  location: string;
  maxParticipants: number;
  currentParticipants: number;
  status: 'PLANNED' | 'ONGOING' | 'COMPLETED';
  createTime: string;
  updateTime: string;
  isDeleted: 0 | 1;
}

export interface Registration {
  registrationId: number;
  trainingId: number;
  cleanerId: number;
  registrationTime: string;
  status: 'REGISTERED' | 'CANCELED';
}

export const mockTrainingList: Training[] = [
  {
    id: 1,
    name: '初级保洁技能培训',
    content: '涵盖基础保洁工具的使用、不同材质表面的清洁方法、安全操作规范等。',
    startTime: '2024-07-15 09:00:00',
    endTime: '2024-07-15 17:00:00',
    location: '总部培训室 A',
    maxParticipants: 30,
    currentParticipants: 25,
    status: 'PLANNED',
    createTime: '2024-06-20 10:00:00',
    updateTime: '2024-06-20 10:00:00',
    isDeleted: 0,
  },
  {
    id: 2,
    name: '家电深度清洗专项培训',
    content: '针对油烟机、空调、洗衣机的内部结构、拆解步骤和专业清洗技巧进行培训。',
    startTime: '2024-07-22 09:00:00',
    endTime: '2024-07-23 17:00:00',
    location: '技术部实验室',
    maxParticipants: 15,
    currentParticipants: 15,
    status: 'PLANNED',
    createTime: '2024-06-21 11:00:00',
    updateTime: '2024-06-21 11:00:00',
    isDeleted: 0,
  },
  {
    id: 3,
    name: '客户沟通与服务礼仪',
    content: '讲解如何与客户进行有效沟通，处理常见问题，以及服务过程中的礼仪规范。',
    startTime: '2024-06-10 14:00:00',
    endTime: '2024-06-10 16:00:00',
    location: '线上会议室',
    maxParticipants: 100,
    currentParticipants: 88,
    status: 'COMPLETED',
    createTime: '2024-05-30 09:00:00',
    updateTime: '2024-06-10 16:05:00',
    isDeleted: 0,
  },
];

export const mockRegistrationList: Registration[] = [
  // 培训1的报名
  { registrationId: 101, trainingId: 1, cleanerId: 1, registrationTime: '2024-06-22 10:00:00', status: 'REGISTERED' },
  { registrationId: 102, trainingId: 1, cleanerId: 2, registrationTime: '2024-06-22 11:00:00', status: 'REGISTERED' },
  { registrationId: 103, trainingId: 1, cleanerId: 3, registrationTime: '2024-06-23 09:30:00', status: 'REGISTERED' },
  { registrationId: 104, trainingId: 1, cleanerId: 4, registrationTime: '2024-06-24 14:00:00', status: 'CANCELED' },
  // 培训2的报名
  { registrationId: 201, trainingId: 2, cleanerId: 5, registrationTime: '2024-06-25 10:00:00', status: 'REGISTERED' },
  { registrationId: 202, trainingId: 2, cleanerId: 6, registrationTime: '2024-06-25 11:00:00', status: 'REGISTERED' },
  // 培训3的报名
  { registrationId: 301, trainingId: 3, cleanerId: 1, registrationTime: '2024-06-01 10:00:00', status: 'REGISTERED' },
  { registrationId: 302, trainingId: 3, cleanerId: 2, registrationTime: '2024-06-01 11:00:00', status: 'REGISTERED' },
  { registrationId: 303, trainingId: 3, cleanerId: 5, registrationTime: '2024-06-02 12:00:00', status: 'REGISTERED' },
]; 