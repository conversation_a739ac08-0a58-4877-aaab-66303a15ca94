<template>
  <div class="notices-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>公司公告</span>
        </div>
      </template>

      <el-table :data="notices" style="width: 100%" v-loading="isLoading" @row-click="showNoticeDetails">
        <el-table-column prop="title" label="标题" />
        <el-table-column prop="noticeType" label="类型" width="120">
          <template #default="scope">
            <el-tag :type="noticeTypeTag(scope.row.noticeType)">{{ noticeTypeMap[scope.row.noticeType] }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="publishTime" label="发布日期" width="180" />
        <el-table-column prop="expireTime" label="失效日期" width="180" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="statusTag(scope.row.status)">{{ statusMap[scope.row.status] }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template #default>
            <el-button type="primary" link>查看</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div v-if="!isLoading && notices.length === 0" class="empty-state">
        <el-empty description="暂无公告" />
      </div>
    </el-card>

    <el-dialog v-model="dialogVisible" :title="selectedNotice?.title" width="50%">
      <div v-if="selectedNotice" class="notice-content">
        <div class="notice-meta">
          <el-tag :type="noticeTypeTag(selectedNotice.noticeType)">{{ noticeTypeMap[selectedNotice.noticeType] }}</el-tag>
          <el-tag :type="statusTag(selectedNotice.status)" style="margin-left: 8px;">{{ statusMap[selectedNotice.status] }}</el-tag>
          <span>发布于: {{ selectedNotice.publishTime }}</span>
        </div>
        <el-divider />
        <p v-html="selectedNotice.content.replace(/\\n/g, '<br>')"></p>
        <el-divider v-if="selectedNotice.expireTime" />
        <div v-if="selectedNotice.expireTime" class="expire-time-display">
          <el-icon><Timer /></el-icon>
          <span>此公告将于 {{ selectedNotice.expireTime }} 失效</span>
        </div>
      </div>
      <template #footer>
        <el-button @click="dialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import type { Notice } from '../../types/notice';

const notices = ref<Notice[]>([]);
const isLoading = ref(true);
const dialogVisible = ref(false);
const selectedNotice = ref<Notice | null>(null);

const noticeTypeMap: { [key: number]: string } = {
  1: '系统通知',
  2: '优惠活动',
  3: '政策更新',
  4: '培训安排'
};

const statusMap: { [key: number]: string } = {
  1: '已发布',
  0: '隐 藏'
};

const noticeTypeTag = (type: number) => {
  switch (type) {
    case 1: return 'info';
    case 2: return 'success';
    case 3: return 'warning';
    case 4: return 'primary';
    default: return 'info';
  }
};

const statusTag = (status: number) => {
  return status === 1 ? 'success' : 'danger';
};

const fetchNotices = () => {
  isLoading.value = true;
  setTimeout(() => {
    console.log("加载模拟公司公告数据...");
    notices.value = [
      {
        noticeId: 1,
        title: '关于2024年端午节放假安排的通知',
        content: '根据国家法定节假日规定，结合公司实际情况，现将2024年端午节放假安排通知如下：\\n6月8日至6月10日放假，共3天。请各位同事安排好工作，祝大家端午安康！',
        noticeType: 3, // POLICY
        status: 1,
        publishTime: '2024-06-05 09:00',
        expireTime: '2024-06-11 00:00'
      },
      {
        noticeId: 2,
        title: '夏季服务着装规范提醒',
        content: '为提升公司形象，确保服务质量，现对夏季着装规范进行重申。请所有家政人员务必穿着公司统一发放的夏季工服，保持整洁，佩戴工牌。感谢配合。',
        noticeType: 1, // SYSTEM
        status: 1,
        publishTime: '2024-06-01 11:30',
        expireTime: '2024-09-01 00:00'
      },
      {
        noticeId: 3,
        title: '【重要】高级母婴护理技能培训报名通知',
        content: '公司将于7月举办高级母婴护理技能培训，旨在提升员工专业技能。名额有限，请有意向的同事于6月20日前到人事部报名。',
        noticeType: 4, // TRAINING
        status: 1,
        publishTime: '2024-05-28 15:00',
        expireTime: '2024-06-21 00:00'
      },
      {
        noticeId: 4,
        title: '年中客户回馈活动上线',
        content: '为感谢新老客户的支持，公司特推出"年中大回馈"优惠活动。活动期间，续费或推荐新客户均有大礼相送。详情请咨询市场部。',
        noticeType: 2, // PROMOTION
        status: 1,
        publishTime: '2024-05-25 10:00',
        expireTime: '2024-07-01 00:00'
      },
      {
        noticeId: 5,
        title: '【草稿】关于秋季团建活动的初步方案',
        content: '秋季团建活动正在策划中，初步方案已出，欢迎大家提出宝贵意见。',
        noticeType: 1, // SYSTEM
        status: 0,
        publishTime: '2024-06-20 18:00',
        expireTime: ''
      }
    ];
    isLoading.value = false;
  }, 500);
};

const showNoticeDetails = (notice: Notice) => {
  selectedNotice.value = notice;
  dialogVisible.value = true;
};

onMounted(fetchNotices);
</script>

<style scoped>
.notices-container {
  padding: 20px;
}
.empty-state {
  margin-top: 20px;
}
.notice-content {
  line-height: 1.8;
}
.notice-meta {
  display: flex;
  /* justify-content: space-between; */
  gap: 16px;
  align-items: center;
  margin-bottom: 15px;
  font-size: 14px;
  color: #909399;
}

.notice-meta > span:last-child {
  margin-left: auto;
}
.expire-time-display {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #e6a23c;
  background-color: #fdf6ec;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
}
</style> 