# 家政服务系统 - 后端项目结构设计

## 1. 项目结构
```bash
src/
├── main/
│   ├── java/
│   │   └── com/example/housekeeper/
│   │       ├── controller/        # 控制器层 (API Endpoints)
│   │       ├── service/           # 业务逻辑层
│   │       ├── mapper/            # 数据访问层 (MyBatis Mapper)
│   │       ├── pojo/              # 数据实体类 (POJO)
│   │       ├── config/            # 配置类
│   │       │
│   │       └── HousekeeperApplication.java # Spring Boot 启动类
│   │
│   └── resources/
│       ├── application.yml            # 主配置文件
│       └── com/example/mapper/        # MyBatis XML映射文件 (根据实际路径调整)
│
└── test/
    └── java/
        └── com/example/housekeeper/
            └── HousekeeperApplicationTests.java
```

## 2. 核心模块说明

### 2.1 用户模块 (user)
- 用户认证
- 用户信息管理
- 角色权限管理

### 2.2 服务模块 (service)
- 服务项目管理
- 服务价格管理

### 2.3 预约模块 (booking)
- 预约管理
- 排班管理

### 2.4 订单模块 (order)
- 订单管理
- 支付管理

### 2.5 反馈模块 (feedback)
- 评价管理
- 投诉管理

### 2.6 培训模块 (training)
- 培训信息管理
- 员工报名管理

## 3. RESTful API 接口设计

### 3.1 用户模块 API (user)

#### 认证接口
```
POST   /api/auth/login                # 用户登录 {username, password}
POST   /api/auth/register            # 用户注册 {username, password, phone, email, userType}
POST   /api/auth/logout              # 用户登出
GET    /api/auth/info                # 获取当前用户信息
```

#### 用户管理接口
```
# 用户基础信息接口
PUT    /api/users/{userId}           # 更新用户基础信息 {
                                      status?: 0|1,           // 账户状态
                                      oldPassword?: string,   // 修改密码时需提供
                                      newPassword?: string,   // 新密码
                                      phone?: string,         // 新手机号
                                      email?: string          // 新邮箱
                                      avatar?: string         // 头像地址
                                    }

# 管理员接口
GET    /api/users/admins             # 获取管理员列表 [查询参数: position, joinTime, status]
POST   /api/users/admins             # 创建管理员 {
                                      userId,
                                      realName,
                                      position,
                                      joinTime
                                    }
PUT    /api/users/admins/{adminId}   # 更新管理员信息 {
                                      realName?,
                                      position?
                                    }
DELETE /api/users/admins/{adminId}   # 删除管理员

# 家政人员接口
GET    /api/users/cleaners           # 获取家政人员列表 [查询参数: status, rating, skills, workYears]
GET    /api/users/cleaners/available # 获取可用的家政人员列表 [查询参数: skills, workYears, rating]
GET    /api/users/cleaners/{cleanerId} # 获取家政人员详情
POST   /api/users/cleaners           # 创建家政人员 {
                                      userId,
                                      realName,
                                      idCard,
                                      gender,
                                      age,
                                      education,
                                      workYears,
                                      qualification,
                                      skills
                                    }
PUT    /api/users/cleaners/{cleanerId} # 更新家政人员信息 {
                                      realName?,
                                      age?,
                                      education?,
                                      workYears?,
                                      qualification?,
                                      skills?,
                                      status?,              // 工作状态 0=空闲, 1=忙碌
                                      rating?               // 评分更新
                                    }
DELETE /api/users/cleaners/{cleanerId} # 删除家政人员

# 客户接口
GET    /api/users/customers          # 获取客户列表 [查询参数: vipLevel, status]
GET    /api/users/customers/{customerId} # 获取客户详情
PUT    /api/users/customers/{customerId} # 更新客户信息 {
                                      realName?,
                                      idCard?,
                                      address?,
                                      preference?,
                                      vipLevel?            // VIP等级 1-5
                                    }
```

### 3.2 服务模块 API (service)

```
# 服务项目管理
GET    /api/services                # 获取服务列表 [查询参数: category, status]
GET    /api/services/{serviceId}    # 获取服务详情
POST   /api/services                # 创建服务 {serviceName, category, description, basePrice, unit, duration}
PUT    /api/services/{serviceId}    # 更新服务
PUT    /api/services/{serviceId}/status # 更新服务状态 {status: 0|1}
DELETE /api/services/{serviceId}    # 删除服务

# 服务价格管理
GET    /api/services/{serviceId}/prices    # 获取服务价格列表
POST   /api/services/{serviceId}/prices    # 创建服务价格 {priceType, price, effectiveTime, expireTime}
PUT    /api/services/{serviceId}/prices/{priceId} # 更新服务价格
DELETE /api/services/{serviceId}/prices/{priceId} # 删除服务价格
```

### 3.3 预约模块 API (booking)

```
# 预约管理
GET    /api/bookings               # 获取预约列表 [查询参数: status, serviceDate]
GET    /api/bookings/{bookingId}   # 获取预约详情
POST   /api/bookings               # 创建预约 {customerId, serviceId, serviceDate, serviceTime, address}
PUT    /api/bookings/{bookingId}/status # 更新预约状态 {status: 0-4}
PUT    /api/bookings/{bookingId}/cleaner # 分配家政人员 {cleanerId}
DELETE /api/bookings/{bookingId}   # 取消预约

# 订单管理
GET    /api/orders                 # 获取订单列表 [查询参数: paymentStatus, paymentMethod]
GET    /api/orders/{orderId}       # 获取订单详情
POST   /api/orders                 # 创建订单 {bookingId, totalAmount, discount}
PUT    /api/orders/{orderId}/pay   # 支付订单 {paymentMethod}
GET    /api/orders/{orderId}/invoice # 获取发票信息
POST   /api/orders/{orderId}/invoice # 申请开具发票 {invoiceInfo}
```

### 3.4 反馈模块 API (feedback)

```
# 服务记录
GET    /api/records                # 获取服务记录列表
GET    /api/records/{recordId}     # 获取服务记录详情
POST   /api/records                # 创建服务记录 {bookingId, cleanerId, startTime, endTime, serviceDetails, photos}

# 评价管理
GET    /api/feedbacks              # 获取评价列表 [查询参数: rating, status]
GET    /api/feedbacks/{feedbackId} # 获取评价详情
POST   /api/feedbacks              # 创建评价 {bookingId, customerId, cleanerId, rating, feedbackText}
PUT    /api/feedbacks/{feedbackId}/reply # 管理员回复 {adminReply}

# 投诉管理
GET    /api/complaints             # 获取投诉列表 [查询参数: status, complaintType]
GET    /api/complaints/{complaintId} # 获取投诉详情
POST   /api/complaints             # 创建投诉 {customerId, bookingId, complaintType, complaintDesc}
PUT    /api/complaints/{complaintId}/status # 更新投诉状态 {status: 0-2}
PUT    /api/complaints/{complaintId}/solution # 提交解决方案 {solution, adminId}
```

### 3.5 系统模块 API (system)

```
# 公告管理
GET    /api/notices               # 获取公告列表 [查询参数: noticeType, status]
GET    /api/notices/{noticeId}    # 获取公告详情
POST   /api/notices               # 发布公告 {adminId, title, content, noticeType, expireTime}
PUT    /api/notices/{noticeId}/status # 更新公告状态 {status: 0|1}
DELETE /api/notices/{noticeId}    # 删除公告

# 统计分析
GET    /api/statistics            # 获取统计数据 [查询参数: statsType, statsPeriod, statsDate]
POST   /api/statistics/generate   # 生成统计数据 {statsType, statsPeriod}

# 智能匹配规则
GET    /api/rules                # 获取匹配规则列表
GET    /api/rules/{ruleId}       # 获取规则详情
POST   /api/rules                # 创建规则 {ruleName, ruleWeight, ruleDesc, ruleParams}
PUT    /api/rules/{ruleId}       # 更新规则
PUT    /api/rules/{ruleId}/status # 更新规则状态 {status: 0|1}
```

### 3.6 培训模块 API (training)

```
# 培训管理
GET    /api/trainings               # 获取培训列表 [查询参数: status, startTime, endTime]
GET    /api/trainings/{trainingId}  # 获取培训详情
POST   /api/trainings               # 创建培训 {name, content, startTime, endTime, location, maxParticipants}
PUT    /api/trainings/{trainingId}  # 更新培训信息 {name?, content?, startTime?, endTime?, location?, maxParticipants?}
PUT    /api/trainings/{trainingId}/status # 更新培训状态 {status: PLANNED|ONGOING|COMPLETED}
DELETE /api/trainings/{trainingId}  # 删除培训 (逻辑删除)

# 培训报名管理
GET    /api/trainings/{trainingId}/participants # 获取某项培训的报名人员列表
POST   /api/trainings/{trainingId}/register     # 员工报名参加培训 {cleanerId}
DELETE /api/trainings/{trainingId}/unregister   # 员工取消报名 {cleanerId}
```

## 4. 统一响应格式

### 4.1 基础响应结构
```json
{
    "code": 200,                 
    "message": "success",         
    "data": {
    },
    "timestamp": 1648888888888,  
    "requestId": "xxxxx"          
}
```

### 4.2 分页数据结构
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "list": [],             
        "pagination": {
            "current": 1,        
            "pageSize": 5,      
            "total": 100,       
            "totalPages": 10    
        }
    }
}
```

### 4.3 状态码设计

#### 4.3.1 HTTP状态码使用
- 200 OK：成功
- 201 Created：创建成功
- 204 No Content：删除成功
- 400 Bad Request：请求参数错误
- 401 Unauthorized：未认证
- 403 Forbidden：无权限
- 404 Not Found：资源不存在
- 500 Internal Server Error：服务器错误

#### 4.3.2 业务状态码
```
# 通用状态码 (1xxxx)
10000: 操作成功
10001: 操作失败
10002: 参数验证失败
10003: 数据不存在
10004: 数据已存在
10005: 系统错误

# 用户模块状态码 (2xxxx)
20001: 用户名或密码错误
20002: 账号已被禁用
20003: token已过期
20004: token无效
20005: 无访问权限
20006: 用户不存在
20007: 密码错误

# 服务模块状态码 (3xxxx)
30001: 服务项目不存在
30002: 服务已下架
30003: 价格信息无效
30004: 服务时间冲突

# 预约模块状态码 (4xxxx)
40001: 预约时间无效
40002: 预约已取消
40003: 重复预约
40004: 家政人员未分配
40005: 预约状态无法更改

# 订单模块状态码 (5xxxx)
50001: 订单不存在
50002: 订单已支付
50003: 支付失败
50004: 退款失败
50005: 发票已开具

# 反馈模块状态码 (6xxxx)
60001: 评价分数无效
60002: 重复评价
60003: 投诉已处理
60004: 反馈内容违规
```

### 4.4 错误响应示例
```json
{
    "code": 20001,
    "message": "用户名或密码错误",
    "data": null,
    "timestamp": 1648888888888,
    "requestId": "xxxxx"
}
```

### 4.5 异常处理规范

#### 4.5.1 全局异常处理
```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    // 业务异常
    @ExceptionHandler(BusinessException.class)
    public ResponseResult<?> handleBusinessException(BusinessException e) {
        return ResponseResult.error(e.getCode(), e.getMessage());
    }
    
    // 参数验证异常
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseResult<?> handleValidException(MethodArgumentNotValidException e) {
        return ResponseResult.error(10002, "参数验证失败");
    }
    
    // 权限异常
    @ExceptionHandler(AccessDeniedException.class)
    public ResponseResult<?> handleAccessDeniedException(AccessDeniedException e) {
        return ResponseResult.error(20005, "无访问权限");
    }
    
    // 系统异常
    @ExceptionHandler(Exception.class)
    public ResponseResult<?> handleException(Exception e) {
        return ResponseResult.error(10005, "系统错误");
    }
}
```

#### 4.5.2 异常使用规范
1. 业务异常应继承自BusinessException
2. 不同模块使用各自的状态码区间
3. 异常信息应该明确且具有指导性
4. 敏感信息不应在异常信息中暴露
5. 所有异常都应该被记录日志

### 4.6 响应规范说明

1. 所有接口必须使用统一响应格式
2. 响应数据应该是自描述的，避免使用模糊的字段名
3. 时间类型统一使用时间戳格式
4. 分页接口统一使用规定的分页格式
5. 列表数据为空时返回空数组，而不是null
6. 金额类数据统一使用字符串类型
7. 布尔值使用1/0表示
8. 枚举值需要在文档中明确定义