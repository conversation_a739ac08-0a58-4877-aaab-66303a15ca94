import service from './index';
import type { BookingQueryParams } from '../types/booking';

/**
 * @description 获取预约列表（家政人员获取自己的，客户/管理员获取其权限内的）
 * @param {BookingQueryParams} params - 查询参数，如 { status: 1 }
 * @returns {Promise<any>}
 */
export const getBookings = (params?: BookingQueryParams): Promise<any> => {
    return service({
        url: '/bookings',
        method: 'get',
        params,
    });
};

/**
 * @description 更新预约状态
 * @param bookingId 预约ID
 * @param status 新的状态
 */
export const updateBookingStatus = (bookingId: number | string, status: number): Promise<any> => {
    return service({
        url: `/bookings/${bookingId}/status`,
        method: 'put',
        data: { status },
    });
}; 