<template>
  <el-container class="admin-dashboard">
    <el-aside width="200px">
      <el-menu router :default-active="$route.path" background-color="#545c64" text-color="#fff" active-text-color="#ffd04b">
        <el-menu-item index="/admin/dashboard">
          <span>仪表盘</span>
        </el-menu-item>
        <el-menu-item index="/admin/cleaners">
          <span>家政人员管理</span>
        </el-menu-item>
        <!-- 其他管理页面的链接可以加在这里 -->
      </el-menu>
    </el-aside>
    <el-main>
      <router-view></router-view>
    </el-main>
  </el-container>
</template>

<script setup lang="ts">
// Layout component, script can be minimal
</script>

<style scoped>
.admin-dashboard {
  height: 100vh;
}
.el-menu {
  border-right: none;
}
.el-menu-item:hover {
    background-color: #434a50 !important;
}
</style> 