<template>
  <div class="feedback-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>收到的客户评价</span>
        </div>
      </template>

      <div v-if="isLoading" class="loading-state">正在加载评价...</div>
      <div v-else-if="feedbacks.length === 0" class="empty-state">
        <el-empty description="暂无客户评价" />
      </div>

      <div v-else class="feedback-list">
        <el-card v-for="fb in feedbacks" :key="fb.feedbackId" class="feedback-card">
          <div class="feedback-header">
            <span class="customer-name">客户: {{ fb.customerName }}</span>
            <span class="feedback-time">{{ fb.createdAt }}</span>
          </div>
          <el-rate
            v-model="fb.rating"
            disabled
            show-score
            text-color="#ff9900"
            score-template="{value} 分"
          />
          <p class="feedback-text">{{ fb.feedbackText }}</p>
          <div v-if="fb.adminReply" class="admin-reply">
            <p><strong>管理员回复:</strong> {{ fb.adminReply }}</p>
          </div>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import type { Feedback } from '../../types/feedback';

const feedbacks = ref<Feedback[]>([]);
const isLoading = ref(true);

const fetchFeedbacks = () => {
  isLoading.value = true;
  // 纯前端模拟
  setTimeout(() => {
    console.log("加载模拟客户评价数据...");
    feedbacks.value = [
      {
        feedbackId: 1,
        bookingId: 101,
        customerId: 201,
        customerName: '王女士',
        cleanerId: 1,
        rating: 5,
        feedbackText: '服务非常周到，打扫得一尘不染，下次还找你！',
        adminReply: '感谢您的认可，我们会继续努力！',
        createdAt: '2024-06-18 12:30'
      },
      {
        feedbackId: 2,
        bookingId: 102,
        customerId: 202,
        customerName: '李先生',
        cleanerId: 1,
        rating: 4,
        feedbackText: '整体不错，但窗户角落还有一点灰尘，希望下次注意。',
        createdAt: '2024-06-15 18:00'
      },
      {
        feedbackId: 3,
        bookingId: 103,
        customerId: 203,
        customerName: '张阿姨',
        cleanerId: 1,
        rating: 5,
        feedbackText: '非常棒的服务！',
        adminReply: '谢谢您的五星好评！',
        createdAt: '2024-06-12 11:00'
      },
    ];
    isLoading.value = false;
  }, 500);
};

onMounted(fetchFeedbacks);
</script>

<style scoped>
.feedback-container {
  padding: 20px;
}
.card-header, .feedback-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.feedback-list {
  display: grid;
  gap: 20px;
}
.feedback-header {
  margin-bottom: 10px;
}
.customer-name {
  font-weight: bold;
}
.feedback-time {
  font-size: 14px;
  color: #909399;
}
.feedback-text {
  margin: 15px 0;
}
.admin-reply {
  background-color: #f4f4f5;
  border-left: 4px solid #909399;
  padding: 10px;
  margin-top: 15px;
  font-size: 14px;
}
</style> 