{"name": "house-web-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "echarts": "^5.6.0", "element-plus": "^2.10.2", "vue": "^3.5.13", "vue-countup-v3": "^1.4.2", "vue-echarts": "^7.0.3", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^24.0.3", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}