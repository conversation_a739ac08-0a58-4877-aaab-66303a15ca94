{"name": "house-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.7.2", "echarts": "^5.6.0", "element-plus": "^2.10.2", "vue": "^3.4.21", "vue-echarts": "^7.0.3", "vue-router": "^4.3.2"}, "devDependencies": {"@types/node": "^20.12.12", "@vitejs/plugin-vue": "^5.0.4", "@vue/tsconfig": "^0.7.0", "typescript": "^5.2.2", "vite": "^5.2.0", "vue-tsc": "^2.0.6"}}