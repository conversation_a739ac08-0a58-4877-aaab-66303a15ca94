<template>
  <div class="loading-spinner-container" :class="{ 'full-screen': fullScreen }">
    <div class="loading-spinner" :class="sizeClass">
      <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path 
          d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" 
          stroke="currentColor" 
          stroke-width="2" 
          stroke-linecap="round" 
          stroke-linejoin="round"
          opacity="0.3"
        />
        <path 
          d="M21 12C21 7.02944 16.9706 3 12 3" 
          stroke="currentColor" 
          stroke-width="2" 
          stroke-linecap="round" 
          stroke-linejoin="round"
          class="spinner-path"
        />
      </svg>
    </div>
    <p v-if="text" class="loading-text">{{ text }}</p>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  fullScreen?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  fullScreen: false,
});

const sizeClass = computed(() => {
  return `spinner-${props.size}`;
});
</script>

<style scoped>
.loading-spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8);
}

.loading-spinner-container.full-screen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  z-index: 9999;
}

.loading-spinner {
  animation: spin 1s linear infinite;
  color: var(--primary-color);
}

.spinner-sm {
  width: 24px;
  height: 24px;
}

.spinner-md {
  width: 48px;
  height: 48px;
}

.spinner-lg {
  width: 72px;
  height: 72px;
}

.spinner-path {
  animation: spinner-dash 1.5s ease-in-out infinite;
}

.loading-text {
  margin-top: var(--spacing-4);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  text-align: center;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes spinner-dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}
</style>
