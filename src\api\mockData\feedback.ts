export interface ServiceRecord {
  recordId: number;
  bookingId: number;
  cleanerId: number;
  startTime: string;
  endTime: string;
  serviceDetails: string;
  photos: string[];
  createTime: string;
}

export interface Feedback {
  feedbackId: number;
  bookingId: number;
  customerId: number;
  cleanerId: number;
  rating: number; // 1-5
  feedbackText: string;
  feedbackTime: string;
  status: number; // 0: 待回复, 1: 已回复
  adminReply: string | null;
  replyTime: string | null;
}

export interface Complaint {
  complaintId: number;
  customerId: number;
  bookingId: number | null;
  complaintType: string;
  complaintDesc: string;
  status: number; // 0: 待处理, 1: 处理中, 2: 已解决
  solution: string | null;
  adminId: number | null;
  complaintTime: string;
}

// --- 模拟数据 ---

export const mockServiceRecordList: ServiceRecord[] = [
  {
    recordId: 1,
    bookingId: 101,
    cleanerId: 1,
    startTime: '2024-06-18 09:00:00',
    endTime: '2024-06-18 11:00:00',
    serviceDetails: '完成了客厅和卧室的日常保洁，更换了垃圾袋。',
    photos: ['https://via.placeholder.com/150/0000FF/FFFFFF?Text=LivingRoom', 'https://via.placeholder.com/150/FF0000/FFFFFF?Text=Bedroom'],
    createTime: '2024-06-18 11:01:35',
  },
  {
    recordId: 2,
    bookingId: 102,
    cleanerId: 2,
    startTime: '2024-06-18 14:00:00',
    endTime: '2024-06-18 15:30:00',
    serviceDetails: '深度清洁了厨房油烟机和灶台。',
    photos: ['https://via.placeholder.com/150/00FF00/FFFFFF?Text=Kitchen'],
    createTime: '2024-06-18 15:32:18',
  },
];

export const mockFeedbackList: Feedback[] = [
  {
    feedbackId: 1,
    bookingId: 101,
    customerId: 1,
    cleanerId: 1,
    rating: 5,
    feedbackText: '王阿姨打扫得非常干净',
    feedbackTime: '2025-06-17 11:46:24',
    status: 1,
    adminReply: '感谢您的认可！',
    replyTime: '2025-06-17 12:07:43',
  },
  {
    feedbackId: 2,
    bookingId: 102,
    customerId: 2,
    cleanerId: 2,
    rating: 5,
    feedbackText: '服务很好，除螨效果',
    feedbackTime: '2025-06-17 11:46:24',
    status: 1,
    adminReply: '谢谢您的反馈。',
    replyTime: '2025-06-17 12:07:43',
  },
  {
    feedbackId: 3,
    bookingId: 103,
    customerId: 3,
    cleanerId: 3,
    rating: 4,
    feedbackText: '师傅很专业，但迟到',
    feedbackTime: '2025-06-17 11:46:24',
    status: 1,
    adminReply: '非常抱歉，我们后续会加强管理。',
    replyTime: '2025-06-17 12:07:43',
  },
  {
    feedbackId: 4,
    bookingId: 104,
    customerId: 4,
    cleanerId: 4,
    rating: 5,
    feedbackText: '很满意',
    feedbackTime: '2025-06-17 11:54:22',
    status: 0,
    adminReply: null,
    replyTime: null,
  },
];

export const mockComplaintList: Complaint[] = [
  {
    complaintId: 1,
    customerId: 3,
    bookingId: 103,
    complaintType: '服务质量',
    complaintDesc: '预约的深度除螨服务，但是感觉和普通清洁差不多，床垫还是感觉不清爽。',
    status: 0, // 待处理
    solution: null,
    adminId: null,
    complaintTime: '2024-06-17 10:00:00',
  },
  {
    complaintId: 2,
    customerId: 4,
    bookingId: null,
    complaintType: '费用问题',
    complaintDesc: '会员充值后，消费时并未享受到对应的会员价优惠。',
    status: 1, // 处理中
    solution: '经核实为系统BUG，已将差价退还至您的账户余额中。',
    adminId: 1,
    complaintTime: '2024-06-16 15:30:00',
  },
]; 