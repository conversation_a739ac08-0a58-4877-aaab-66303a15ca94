<template>
  <el-card class="page-card">
    <template #header>
      <div class="card-header">
        <span>培训列表</span>
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>
          创建培训
        </el-button>
      </div>
    </template>

    <el-table :data="trainingList" style="width: 100%" height="100%">
      <el-table-column prop="name" label="培训名称" width="250" show-overflow-tooltip />
      <el-table-column label="培训时间" width="320">
        <template #default="{ row }">
          {{ row.startTime }} 至 {{ row.endTime }}
        </template>
      </el-table-column>
      <el-table-column prop="location" label="地点" width="150" />
      <el-table-column label="报名人数" width="120">
         <template #default="{ row }">
          {{ row.currentParticipants }} / {{ row.maxParticipants }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="120">
         <template #default="{ row }">
            <el-tag :type="statusMap[row.status].type">{{ statusMap[row.status].text }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="150">
        <template #default="{ row }">
          <el-button size="small" type="primary" @click="handleEdit(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-card>

  <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px" @close="resetForm">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
      <el-form-item label="培训名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入培训名称" />
      </el-form-item>
       <el-form-item label="培训时间" prop="trainingTime">
        <el-date-picker
          v-model="form.trainingTime"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item label="培训地点" prop="location">
        <el-input v-model="form.location" placeholder="请输入培训地点" />
      </el-form-item>
       <el-form-item label="最大人数" prop="maxParticipants">
        <el-input-number v-model="form.maxParticipants" :min="1" />
      </el-form-item>
       <el-form-item label="培训状态" prop="status">
        <el-select v-model="form.status" placeholder="请选择培训状态">
          <el-option label="计划中" value="PLANNED"></el-option>
          <el-option label="进行中" value="ONGOING"></el-option>
          <el-option label="已完成" value="COMPLETED"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="培训内容" prop="content">
        <el-input v-model="form.content" type="textarea" :rows="5" placeholder="请输入培训内容" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitForm">提交</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { mockTrainingList, type Training } from '@/api/mockData/training';
import { Plus } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus';

type TrainingForm = Omit<Training, 'startTime' | 'endTime'> & { trainingTime: [string, string] | null };

const trainingList = ref<Training[]>([]);
const dialogVisible = ref(false);
const dialogTitle = ref('');
const formRef = ref<FormInstance>();

const getInitialFormState = (): TrainingForm => ({
  id: 0,
  name: '',
  content: '',
  trainingTime: null,
  location: '',
  maxParticipants: 20,
  currentParticipants: 0,
  status: 'PLANNED',
  createTime: '',
  updateTime: '',
  isDeleted: 0,
});

let form = reactive<TrainingForm>(getInitialFormState());

const rules = reactive<FormRules>({
  name: [{ required: true, message: '请输入培训名称', trigger: 'blur' }],
  trainingTime: [{ required: true, message: '请选择培训时间', trigger: 'change' }],
  location: [{ required: true, message: '请输入培训地点', trigger: 'blur' }],
  maxParticipants: [{ required: true, message: '请输入最大参与人数', trigger: 'blur' }],
  status: [{ required: true, message: '请选择培训状态', trigger: 'change' }],
  content: [{ required: true, message: '请输入培训内容', trigger: 'blur' }],
});

const statusMap: { [key: string]: { text: string; type: 'primary' | 'success' | 'info' } } = {
    PLANNED: { text: '计划中', type: 'primary' },
    ONGOING: { text: '进行中', type: 'success' },
    COMPLETED: { text: '已完成', type: 'info' },
};

onMounted(() => {
  trainingList.value = JSON.parse(JSON.stringify(mockTrainingList.filter(t => t.isDeleted === 0)));
});

const resetForm = () => {
  Object.assign(form, getInitialFormState());
  formRef.value?.clearValidate();
};

const handleCreate = () => {
  resetForm();
  dialogTitle.value = '创建新培训';
  dialogVisible.value = true;
};

const handleEdit = (row: Training) => {
  dialogTitle.value = '编辑培训';
  const { startTime, endTime, ...rest } = row;
  const trainingTime: [string, string] = [startTime, endTime];
  Object.assign(form, { ...rest, trainingTime });
  dialogVisible.value = true;
};

const handleDelete = (id: number) => {
  ElMessageBox.confirm('确定要删除此培训吗? 这将是一个逻辑删除。', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    const index = trainingList.value.findIndex(t => t.id === id);
    if (index !== -1) {
      // 逻辑删除，也可以直接 splice 删除
      // trainingList.value[index].isDeleted = 1;
      trainingList.value.splice(index, 1);
      ElMessage.success('删除成功');
    }
  });
};

const submitForm = async () => {
  if (!formRef.value) return;
  await formRef.value.validate((valid) => {
    if (valid && form.trainingTime) {
      const [startTime, endTime] = form.trainingTime;
      const { trainingTime: removed, ...submissionData } = form;

      if (submissionData.id) { // Edit
        const index = trainingList.value.findIndex(t => t.id === submissionData.id);
        if (index !== -1) {
          const finalData = { ...trainingList.value[index], ...submissionData, startTime, endTime };
          finalData.updateTime = new Date().toLocaleString('sv-SE');
          trainingList.value[index] = finalData;
          ElMessage.success('更新成功');
          console.log('培训更新成功:', JSON.parse(JSON.stringify(finalData)));
        }
      } else { // Create
        const newTraining: Training = {
          ...submissionData,
          startTime,
          endTime,
          id: Math.max(...trainingList.value.map(t => t.id), 0) + 1,
          createTime: new Date().toLocaleString('sv-SE'),
          updateTime: new Date().toLocaleString('sv-SE'),
          currentParticipants: 0,
          isDeleted: 0
        };
        trainingList.value.unshift(newTraining);
        ElMessage.success('创建成功');
        console.log('培训创建成功:', JSON.parse(JSON.stringify(newTraining)));
      }
      dialogVisible.value = false;
    }
  });
};
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}
:deep(.el-card__body) {
  flex: 1;
  overflow: hidden;
}
.el-date-editor {
    width: 100%;
}
</style> 