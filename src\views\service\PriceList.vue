<template>
  <el-card class="page-card">
    <template #header>
      <div class="card-header">
        <span>价格列表</span>
        <el-button class="button" type="primary" @click="handleAdd">新增价格</el-button>
      </div>
    </template>

    <el-table :data="priceList" style="width: 100%" height="100%">
      <el-table-column prop="priceId" label="价格ID" width="80" />
      <el-table-column prop="priceType" label="价格类型" />
      <el-table-column prop="price" label="价格" />
      <el-table-column prop="effectiveTime" label="生效时间" width="180"/>
      <el-table-column prop="expireTime" label="失效时间" width="180">
        <template #default="scope">
          {{ scope.row.expireTime || '长期有效' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="scope">
          <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-card>

  <el-dialog v-model="dialogVisible" :title="dialogTitle" width="500px">
    <el-form :model="priceForm" :rules="rules" ref="priceFormRef" label-width="90px">
       <el-form-item label="所属服务" prop="serviceId">
        <el-select v-model="priceForm.serviceId" placeholder="请选择所属服务" style="width: 100%;">
          <el-option v-for="service in serviceList" :key="service.serviceId" :label="service.serviceName" :value="service.serviceId" />
        </el-select>
      </el-form-item>
      <el-form-item label="价格类型" prop="priceType">
        <el-input v-model="priceForm.priceType" />
      </el-form-item>
      <el-form-item label="价格" prop="price">
        <el-input-number v-model="priceForm.price" :min="0" :precision="2" />
      </el-form-item>
      <el-form-item label="生效时间" prop="effectiveTime">
         <el-date-picker v-model="priceForm.effectiveTime" type="datetime" placeholder="选择生效时间" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%;" />
      </el-form-item>
      <el-form-item label="失效时间" prop="expireTime">
        <el-date-picker v-model="priceForm.expireTime" type="datetime" placeholder="留空表示长期有效" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%;" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive, computed } from 'vue';
import { mockServicePriceList, mockServiceList, type Service, type ServicePrice } from '@/api/mockData/service';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage, ElMessageBox } from 'element-plus';

const priceList = ref<ServicePrice[]>(JSON.parse(JSON.stringify(mockServicePriceList)));
const serviceList = ref<Service[]>(JSON.parse(JSON.stringify(mockServiceList)));

const dialogVisible = ref(false);
const dialogTitle = ref('');
const dialogMode = ref<'add' | 'edit'>('add');
const priceFormRef = ref<FormInstance>();

const initialFormState: Omit<ServicePrice, 'priceId'> = {
  serviceId: undefined,
  priceType: '',
  price: 0,
  effectiveTime: '',
  expireTime: null,
};
const priceForm = reactive<any>({ ...initialFormState });

const rules = reactive<FormRules>({
  serviceId: [{ required: true, message: '请选择所属服务', trigger: 'change' }],
  priceType: [{ required: true, message: '请输入价格类型', trigger: 'blur' }],
  price: [{ required: true, message: '请输入价格', trigger: 'blur' }],
  effectiveTime: [{ required: true, message: '请选择生效时间', trigger: 'change' }],
});

const handleAdd = () => {
  dialogMode.value = 'add';
  dialogTitle.value = '新增价格';
  Object.assign(priceForm, initialFormState);
  priceFormRef.value?.clearValidate();
  dialogVisible.value = true;
};

const handleEdit = (row: ServicePrice) => {
  dialogMode.value = 'edit';
  dialogTitle.value = '编辑价格';
  const rowData = JSON.parse(JSON.stringify(row));
  Object.assign(priceForm, rowData);
  dialogVisible.value = true;
};

const handleDelete = (row: ServicePrice) => {
   ElMessageBox.confirm(`确定要删除价格类型 "${row.priceType}" 吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    const index = priceList.value.findIndex(p => p.priceId === row.priceId);
    if (index !== -1) {
      priceList.value.splice(index, 1);
      ElMessage.success('删除成功');
      console.log(`最新价格列表:`, JSON.parse(JSON.stringify(priceList.value)));
    }
  }).catch(() => {});
};

const submitForm = async () => {
  if (!priceFormRef.value) return;
  await priceFormRef.value.validate((valid) => {
    if (valid) {
      if (dialogMode.value === 'add') {
        const newPrice: ServicePrice = {
          priceId: Math.max(0, ...priceList.value.map(p => p.priceId)) + 1,
          ...priceForm,
        };
        priceList.value.unshift(newPrice);
        ElMessage.success('新增成功');
      } else {
        const priceIdToUpdate = priceForm.priceId;
        const index = priceList.value.findIndex(p => p.priceId === priceIdToUpdate);
        if (index !== -1) {
          priceList.value[index] = { ...priceForm };
          ElMessage.success('更新成功');
        }
      }
      console.log(`最新价格列表:`, JSON.parse(JSON.stringify(priceList.value)));
      dialogVisible.value = false;
    }
  });
};

onMounted(() => {
  console.log(`获取到所有服务的价格列表:`, JSON.parse(JSON.stringify(priceList.value)));
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}
:deep(.el-card__body) {
  flex: 1;
  overflow: hidden;
}
</style> 