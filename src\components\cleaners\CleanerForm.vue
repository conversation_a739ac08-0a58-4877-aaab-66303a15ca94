<template>
  <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
    <el-form-item label="用户ID" prop="userId">
      <el-input-number v-model="form.userId" :min="1"></el-input-number>
    </el-form-item>
    <el-form-item label="真实姓名" prop="realName">
      <el-input v-model="form.realName"></el-input>
    </el-form-item>
    <el-form-item label="身份证号" prop="idCard">
      <el-input v-model="form.idCard"></el-input>
    </el-form-item>
    <el-form-item label="性别" prop="gender">
       <el-radio-group v-model="form.gender">
        <el-radio :label="1">男</el-radio>
        <el-radio :label="0">女</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="年龄" prop="age">
      <el-input-number v-model="form.age" :min="18" :max="65"></el-input-number>
    </el-form-item>
    <el-form-item label="学历" prop="education">
      <el-input v-model="form.education"></el-input>
    </el-form-item>
    <el-form-item label="工作年限" prop="workYears">
      <el-input-number v-model="form.workYears" :min="0"></el-input-number>
    </el-form-item>
    <el-form-item label="资格证书" prop="qualification">
      <el-input v-model="form.qualification"></el-input>
    </el-form-item>
    <el-form-item label="技能" prop="skills">
      <el-select v-model="form.skills" multiple placeholder="请选择技能">
        <el-option label="日常保洁" value="日常保洁"></el-option>
        <el-option label="深度保洁" value="深度保洁"></el-option>
        <el-option label="家电清洗" value="家电清洗"></el-option>
        <el-option label="做饭" value="做饭"></el-option>
      </el-select>
    </el-form-item>
     <el-form-item label="工作状态" prop="status">
       <el-radio-group v-model="form.status">
        <el-radio :label="0">空闲</el-radio>
        <el-radio :label="1">忙碌</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="submitForm">提交</el-button>
      <el-button @click="emit('close')">取消</el-button>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import { createCleaner, updateCleaner } from '../../api/user';
import type { Cleaner } from '../../types/user';

const props = defineProps<{
  cleaner?: Cleaner;
}>();

const emit = defineEmits(['close']);

const formRef = ref<FormInstance>();

const form = reactive<Cleaner>({
  id: undefined,
  userId: 1, // Default or fetch from a user list
  realName: '',
  idCard: '',
  gender: 1,
  age: 18,
  education: '',
  workYears: 0,
  qualification: '',
  skills: [],
  status: 0,
  rating: 5, // Default rating
});

const rules = reactive<FormRules>({
  userId: [{ required: true, message: '请输入用户ID', trigger: 'blur' }],
  realName: [{ required: true, message: '请输入真实姓名', trigger: 'blur' }],
  idCard: [{ required: true, message: '请输入身份证号', trigger: 'blur' }],
  gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
  age: [{ required: true, message: '请输入年龄', trigger: 'blur' }],
  workYears: [{ required: true, message: '请输入工作年限', trigger: 'blur' }],
  status: [{ required: true, message: '请选择工作状态', trigger: 'change' }],
});

onMounted(() => {
  if (props.cleaner) {
    Object.assign(form, props.cleaner);
  }
});

const submitForm = async () => {
  if (!formRef.value) return;
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (form.id) {
          await updateCleaner(form.id, form);
          ElMessage.success('更新成功');
        } else {
          await createCleaner(form);
          ElMessage.success('创建成功');
        }
        emit('close', true);
      } catch (error) {
        ElMessage.error('操作失败');
      }
    }
  });
};
</script> 