/**
 * @description 客户反馈信息
 */
export interface Feedback {
  feedbackId: number | string;
  bookingId: number | string;
  customerId: number;
  customerName?: string; // 假设会提供
  cleanerId: number;
  rating: number; // 评分 1-5
  feedbackText: string;
  adminReply?: string;
  createdAt?: string; // 评价创建时间
}

/**
 * @description 获取反馈列表的查询参数
 */
export interface FeedbackQueryParams {
  cleanerId?: number;
  rating?: number;
} 