<template>
  <div class="cleaner-profile-page">
    <div class="page-header">
      <h1 class="page-title">个人资料</h1>
      <p class="page-subtitle">管理您的个人信息和专业技能</p>
    </div>

    <div class="profile-content">
      <div class="profile-card">
        <div class="avatar-section">
          <el-avatar :size="120" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
          <h2>张三</h2>
          <p>家政服务人员</p>
          <div class="rating-display">
            <el-rate v-model="rating" disabled />
            <span>{{ rating }} 分</span>
          </div>
        </div>

        <div class="info-section">
          <el-form :model="profileForm" label-width="100px">
            <el-form-item label="真实姓名">
              <el-input v-model="profileForm.name" :disabled="!isEditing" />
            </el-form-item>
            <el-form-item label="手机号码">
              <el-input v-model="profileForm.phone" :disabled="!isEditing" />
            </el-form-item>
            <el-form-item label="身份证号">
              <el-input v-model="profileForm.idCard" disabled />
            </el-form-item>
            <el-form-item label="工作年限">
              <el-input-number v-model="profileForm.experience" :disabled="!isEditing" />
            </el-form-item>
            <el-form-item label="专业技能">
              <el-select v-model="profileForm.skills" multiple :disabled="!isEditing" style="width: 100%">
                <el-option label="日常保洁" value="日常保洁" />
                <el-option label="深度清洁" value="深度清洁" />
                <el-option label="家电清洗" value="家电清洗" />
                <el-option label="开荒保洁" value="开荒保洁" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button v-if="!isEditing" type="primary" @click="isEditing = true">
                编辑资料
              </el-button>
              <div v-else>
                <el-button type="primary" @click="saveProfile">保存</el-button>
                <el-button @click="cancelEdit">取消</el-button>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';

const isEditing = ref(false);
const rating = ref(4.8);

const profileForm = reactive({
  name: '张三',
  phone: '138****1234',
  idCard: '110101199001011234',
  experience: 5,
  skills: ['日常保洁', '深度清洁']
});

const originalForm = { ...profileForm };

const saveProfile = () => {
  ElMessage.success('个人资料保存成功');
  isEditing.value = false;
};

const cancelEdit = () => {
  Object.assign(profileForm, originalForm);
  isEditing.value = false;
};
</script>

<style scoped>
.cleaner-profile-page {
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #6b7280;
  margin: 0;
}

.profile-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: grid;
  grid-template-columns: 300px 1fr;
}

.avatar-section {
  background: linear-gradient(135deg, #4f46e5, #06b6d4);
  color: white;
  padding: 40px;
  text-align: center;
}

.avatar-section h2 {
  margin: 16px 0 4px 0;
  font-size: 24px;
}

.avatar-section p {
  margin: 0 0 16px 0;
  opacity: 0.9;
}

.rating-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.info-section {
  padding: 40px;
}

@media (max-width: 768px) {
  .profile-card {
    grid-template-columns: 1fr;
  }
  
  .avatar-section {
    padding: 30px 20px;
  }
  
  .info-section {
    padding: 30px 20px;
  }
}
</style>
