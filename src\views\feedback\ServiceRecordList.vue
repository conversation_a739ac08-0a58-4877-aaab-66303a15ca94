<template>
  <el-card class="page-card">
    <template #header>
      <div class="card-header">
        <span>服务记录列表</span>
      </div>
    </template>

    <el-table :data="serviceRecordList" style="width: 100%" height="100%">
      <el-table-column prop="recordId" label="ID" width="60" />
      <el-table-column prop="bookingId" label="关联预定ID" width="110" />
      <el-table-column prop="startTime" label="开始时间" width="180" />
      <el-table-column prop="endTime" label="结束时间" width="180" />
      <el-table-column prop="createTime" label="创建时间" width="180" />
      <el-table-column prop="serviceDetails" label="服务详情" show-overflow-tooltip />
      <el-table-column label="服务照片" width="120">
        <template #default="scope">
          <el-button v-if="scope.row.photos && scope.row.photos.length > 0" size="small" type="primary" @click="handleViewPhotos(scope.row)">
            查看 ({{ scope.row.photos.length }})
          </el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>
    </el-table>
  </el-card>

  <el-dialog v-model="dialogVisible" title="查看服务照片" width="600px">
    <div class="photo-gallery">
       <el-image
        v-for="(photo, index) in currentPhotos"
        :key="index"
        style="width: 150px; height: 150px; margin: 10px;"
        :src="photo"
        :preview-src-list="currentPhotos"
        :initial-index="index"
        fit="cover"
      />
    </div>
  </el-dialog>

</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { mockServiceRecordList, type ServiceRecord } from '@/api/mockData/feedback';

const serviceRecordList = ref<ServiceRecord[]>(JSON.parse(JSON.stringify(mockServiceRecordList)));
const dialogVisible = ref(false);
const currentPhotos = ref<string[]>([]);

const handleViewPhotos = (row: any) => {
  currentPhotos.value = row.photos;
  dialogVisible.value = true;
};


onMounted(() => {
  console.log('页面加载时，服务记录数据:', JSON.parse(JSON.stringify(serviceRecordList.value)));
});

</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}
:deep(.el-card__body) {
  flex: 1;
  overflow: hidden;
}
.photo-gallery {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}
</style> 