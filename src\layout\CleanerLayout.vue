<template>
  <div class="cleaner-dashboard">
    <!-- 侧边栏 -->
    <aside class="sidebar" :class="{ collapsed: sidebarCollapsed }">
      <div class="sidebar-header">
        <div class="logo-container">
          <div class="logo-icon">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M9 22V12H15V22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <h2 v-if="!sidebarCollapsed" class="logo-text">家政服务</h2>
        </div>
        <button class="sidebar-toggle" @click="toggleSidebar">
          <el-icon><Menu /></el-icon>
        </button>
      </div>

      <nav class="sidebar-nav">
        <ul class="nav-list">
          <li class="nav-item">
            <router-link to="/cleaner/dashboard" class="nav-link" :class="{ active: $route.path === '/cleaner/dashboard' }">
              <el-icon class="nav-icon"><House /></el-icon>
              <span v-if="!sidebarCollapsed" class="nav-text">工作台</span>
            </router-link>
          </li>
          <li class="nav-item">
            <router-link to="/cleaner/tasks" class="nav-link" :class="{ active: $route.path === '/cleaner/tasks' }">
              <el-icon class="nav-icon"><List /></el-icon>
              <span v-if="!sidebarCollapsed" class="nav-text">我的任务</span>
            </router-link>
          </li>
          <li class="nav-item">
            <router-link to="/cleaner/profile" class="nav-link" :class="{ active: $route.path === '/cleaner/profile' }">
              <el-icon class="nav-icon"><User /></el-icon>
              <span v-if="!sidebarCollapsed" class="nav-text">个人资料</span>
            </router-link>
          </li>
          <li class="nav-item">
            <router-link to="/cleaner/training" class="nav-link" :class="{ active: $route.path === '/cleaner/training' }">
              <el-icon class="nav-icon"><Reading /></el-icon>
              <span v-if="!sidebarCollapsed" class="nav-text">培训中心</span>
            </router-link>
          </li>
          <li class="nav-item">
            <router-link to="/cleaner/records" class="nav-link" :class="{ active: $route.path === '/cleaner/records' }">
              <el-icon class="nav-icon"><Document /></el-icon>
              <span v-if="!sidebarCollapsed" class="nav-text">服务记录</span>
            </router-link>
          </li>
          <li class="nav-item">
            <router-link to="/cleaner/feedback" class="nav-link" :class="{ active: $route.path === '/cleaner/feedback' }">
              <el-icon class="nav-icon"><ChatDotSquare /></el-icon>
              <span v-if="!sidebarCollapsed" class="nav-text">客户评价</span>
            </router-link>
          </li>
          <li class="nav-item">
            <router-link to="/cleaner/notices" class="nav-link" :class="{ active: $route.path === '/cleaner/notices' }">
              <el-icon class="nav-icon"><Bell /></el-icon>
              <span v-if="!sidebarCollapsed" class="nav-text">公司公告</span>
            </router-link>
          </li>
          <li class="nav-item">
            <router-link to="/cleaner/report" class="nav-link" :class="{ active: $route.path === '/cleaner/report' }">
              <el-icon class="nav-icon"><DataLine /></el-icon>
              <span v-if="!sidebarCollapsed" class="nav-text">工作报告</span>
            </router-link>
          </li>
        </ul>
      </nav>

      <div class="sidebar-footer">
        <button class="logout-btn" @click="logout">
          <el-icon class="logout-icon"><SwitchButton /></el-icon>
          <span v-if="!sidebarCollapsed" class="logout-text">退出登录</span>
        </button>
      </div>
    </aside>

    <!-- 主内容区域 -->
    <main class="main-content" :class="{ expanded: sidebarCollapsed }">
      <header class="main-header">
        <div class="header-left">
          <div class="breadcrumb">
            <span class="breadcrumb-item">{{ currentPageTitle }}</span>
          </div>
        </div>
        <div class="header-right">
          <div class="user-info">
            <el-avatar :size="36" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
            <span class="user-name">张三</span>
          </div>
        </div>
      </header>
      
      <div class="main-body">
        <router-view />
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { 
  Menu, House, List, User, Reading, Document, 
  ChatDotSquare, Bell, DataLine, SwitchButton 
} from '@element-plus/icons-vue';

const router = useRouter();
const route = useRoute();

const sidebarCollapsed = ref(false);

const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value;
};

const logout = () => {
  ElMessage.success('退出登录成功');
  router.push('/login');
};

// 根据当前路由获取页面标题
const currentPageTitle = computed(() => {
  const titleMap: Record<string, string> = {
    '/cleaner/dashboard': '工作台',
    '/cleaner/tasks': '我的任务',
    '/cleaner/profile': '个人资料',
    '/cleaner/training': '培训中心',
    '/cleaner/records': '服务记录',
    '/cleaner/feedback': '客户评价',
    '/cleaner/notices': '公司公告',
    '/cleaner/report': '工作报告',
  };
  return titleMap[route.path] || '家政服务';
});
</script>

<style scoped>
.cleaner-dashboard {
  display: flex;
  height: 100vh;
  background: #f5f7fa;
}

/* 侧边栏样式 */
.sidebar {
  width: 280px;
  background: linear-gradient(180deg, #4f46e5 0%, #3730a3 100%);
  color: white;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 100;
}

.sidebar.collapsed {
  width: 80px;
}

.sidebar-header {
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.logo-icon svg {
  width: 24px;
  height: 24px;
  color: white;
}

.logo-text {
  font-size: 18px;
  font-weight: 700;
  margin: 0;
  white-space: nowrap;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.sidebar-toggle:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 导航样式 */
.sidebar-nav {
  flex: 1;
  padding: 16px 0;
}

.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin-bottom: 4px;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.2s;
  border-radius: 0;
  position: relative;
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.nav-link.active {
  background: rgba(255, 255, 255, 0.15);
  color: white;
}

.nav-link.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: white;
}

.nav-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.nav-text {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

/* 侧边栏底部 */
.sidebar-footer {
  padding: 16px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.logout-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 12px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 12px;
  color: white;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 14px;
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.logout-icon {
  width: 18px;
  height: 18px;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.main-header {
  background: white;
  padding: 0 24px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.breadcrumb-item {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.main-body {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .sidebar.collapsed {
    width: 280px;
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .main-header {
    padding: 0 16px;
  }
  
  .main-body {
    padding: 16px;
  }
}
</style>
