<template>
  <el-dialog
    title="创建服务记录"
    :model-value="true"
    width="600px"
    :before-close="handleClose"
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
      <el-form-item label="关联预约" prop="bookingId">
        <el-select v-model="form.bookingId" placeholder="请选择已完成的预约">
          <!-- 在真实应用中，这里会从API获取已完成的预约列表 -->
          <el-option label="预约单 #101 (张三)" value="101"></el-option>
          <el-option label="预约单 #105 (李四)" value="105"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="服务时间" required>
        <el-col :span="11">
          <el-form-item prop="startTime">
             <el-date-picker v-model="form.startTime" type="datetime" placeholder="开始时间" style="width: 100%;"></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col class="text-center" :span="2">-</el-col>
        <el-col :span="11">
          <el-form-item prop="endTime">
            <el-date-picker v-model="form.endTime" type="datetime" placeholder="结束时间" style="width: 100%;"></el-date-picker>
          </el-form-item>
        </el-col>
      </el-form-item>
      <el-form-item label="服务详情" prop="serviceDetails">
        <el-input v-model="form.serviceDetails" type="textarea" :rows="4" placeholder="请详细描述服务内容..."></el-input>
      </el-form-item>
      <el-form-item label="现场照片" prop="photos">
        <el-upload
          v-model:file-list="fileList"
          action="#"
          list-type="picture-card"
          :before-upload="() => false" 
          :on-preview="handlePictureCardPreview"
          :on-remove="handleRemove"
          multiple>
          <el-icon><Plus /></el-icon>
        </el-upload>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submitForm">提 交</el-button>
      </span>
    </template>
  </el-dialog>
  
  <el-dialog v-model="dialogVisible">
    <img w-full :src="dialogImageUrl" alt="Preview Image" />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormInstance, FormRules, UploadProps, UploadUserFile } from 'element-plus';
import { createRecord } from '../../api/record';
import type { ServiceRecord } from '../../types/record';

const emit = defineEmits(['close']);

const formRef = ref<FormInstance>();
const form = reactive<ServiceRecord>({
  bookingId: '',
  cleanerId: 1, // 假设当前家政人员ID为1
  startTime: '',
  endTime: '',
  serviceDetails: '',
  photos: []
});

const rules = reactive<FormRules>({
  bookingId: [{ required: true, message: '请选择一个预约', trigger: 'change' }],
  startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  endTime: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
  serviceDetails: [{ required: true, message: '请填写服务详情', trigger: 'blur' }],
});

const fileList = ref<UploadUserFile[]>([]);
const dialogImageUrl = ref('');
const dialogVisible = ref(false);

const handleRemove: UploadProps['onRemove'] = (uploadFile, uploadFiles) => {
  console.log(uploadFile, uploadFiles);
};

const handlePictureCardPreview: UploadProps['onPreview'] = (uploadFile) => {
  dialogImageUrl.value = uploadFile.url!;
  dialogVisible.value = true;
};

const handleClose = () => {
  emit('close', false);
};

const submitForm = async () => {
  if (!formRef.value) return;
  await formRef.value.validate(async (valid) => {
    if (valid) {
      // 在真实应用中，这里会上传图片获取URL，再赋值给form.photos
      // 在模拟阶段，我们直接使用本地的blob url (虽然它很快会失效)
      form.photos = fileList.value.map(file => file.url || '');
      
      try {
        await createRecord(form);
        ElMessage.success('服务记录创建成功（模拟）！');
        emit('close', true); // 发送成功信号，让父组件刷新列表
      } catch (error) {
         if (error && !(error as any).__MOCK__) {
            ElMessage.error('创建失败');
         }
      }
    }
  });
};
</script>

<style scoped>
.text-center {
  text-align: center;
}
</style> 