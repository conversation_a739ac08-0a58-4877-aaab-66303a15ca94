<template>
  <el-card class="page-card">
    <template #header>
      <div class="card-header">
        <span>领用记录</span>
        <el-button type="primary" @click="handleOpenCheckoutDialog">
          <el-icon><Plus /></el-icon>
          领用登记
        </el-button>
      </div>
    </template>

    <el-table :data="logTableData" style="width: 100%" height="100%">
      <el-table-column prop="logId" label="记录ID" width="100" />
      <el-table-column prop="resourceName" label="领用资源" width="200" show-overflow-tooltip />
      <el-table-column prop="cleanerName" label="领用人" width="120" />
      <el-table-column prop="checkoutTime" label="领用时间" width="180" />
      <el-table-column prop="expectedReturnTime" label="预计归还时间" width="180" />
      <el-table-column prop="actualReturnTime" label="实际归还时间" width="180">
         <template #default="{ row }">
            {{ row.actualReturnTime || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="120">
         <template #default="{ row }">
            <el-tag :type="row.status === '已领用' ? 'warning' : 'success'">{{ row.status }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="100">
        <template #default="{ row }">
          <el-button v-if="row.status === '已领用'" size="small" type="primary" @click="handleReturn(row.logId)">归还</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-card>

  <el-dialog v-model="checkoutDialogVisible" title="领用登记" width="600px" @close="resetCheckoutForm">
    <el-form :model="checkoutForm" :rules="checkoutRules" ref="checkoutFormRef" label-width="120px">
      <el-form-item label="选择资源" prop="resourceId">
        <el-select v-model="checkoutForm.resourceId" placeholder="请选择闲置的资源">
          <el-option
            v-for="resource in availableResources"
            :key="resource.resourceId"
            :label="`${resource.resourceName} (${resource.model || 'N/A'})`"
            :value="resource.resourceId"
          />
        </el-select>
      </el-form-item>
       <el-form-item label="选择领用人" prop="cleanerId">
        <el-select v-model="checkoutForm.cleanerId" placeholder="请选择领用人">
          <el-option
            v-for="cleaner in allCleaners"
            :key="cleaner.cleanerId"
            :label="cleaner.realName"
            :value="cleaner.cleanerId"
          />
        </el-select>
      </el-form-item>
       <el-form-item label="预计归还时间" prop="expectedReturnTime">
          <el-date-picker 
            v-model="checkoutForm.expectedReturnTime" 
            type="datetime" 
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width:100%" />
      </el-form-item>
       <el-form-item label="备注" prop="notes">
        <el-input v-model="checkoutForm.notes" type="textarea" :rows="3" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="checkoutDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitCheckoutForm">提交</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { mockResourceList, mockResourceLogList, type Resource, type ResourceLog } from '@/api/mockData/resource';
import { mockCleanerList } from '@/api/mockData/user';
import { Plus } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus';

type CheckoutForm = Omit<ResourceLog, 'logId' | 'checkoutTime' | 'actualReturnTime' | 'status'>

const allResources = ref<Resource[]>(JSON.parse(JSON.stringify(mockResourceList)));
const resourceLogs = ref<ResourceLog[]>(JSON.parse(JSON.stringify(mockResourceLogList)));
const allCleaners = ref(mockCleanerList);

const checkoutDialogVisible = ref(false);
const checkoutFormRef = ref<FormInstance>();

const getInitialCheckoutFormState = (): CheckoutForm => ({
    resourceId: NaN,
    cleanerId: NaN,
    expectedReturnTime: '',
    notes: '',
});

let checkoutForm = reactive<CheckoutForm>(getInitialCheckoutFormState());

const checkoutRules = reactive<FormRules>({
    resourceId: [{ required: true, message: '请选择资源', trigger: 'change' }],
    cleanerId: [{ required: true, message: '请选择领用人', trigger: 'change' }],
    expectedReturnTime: [{ required: true, message: '请选择预计归还时间', trigger: 'change' }],
});

const resourceMap = computed(() => new Map(allResources.value.map(r => [r.resourceId, r.resourceName])));
const cleanerMap = computed(() => new Map(allCleaners.value.map(c => [c.cleanerId, c.realName])));
const availableResources = computed(() => allResources.value.filter(r => r.status === 2)); // 2: 闲置

const logTableData = computed(() => {
    return resourceLogs.value.map(log => ({
        ...log,
        resourceName: resourceMap.value.get(log.resourceId) || '未知资源',
        cleanerName: cleanerMap.value.get(log.cleanerId) || '未知人员',
    })).sort((a,b) => b.logId - a.logId);
});

const resetCheckoutForm = () => {
  Object.assign(checkoutForm, getInitialCheckoutFormState());
  checkoutFormRef.value?.clearValidate();
};

const handleOpenCheckoutDialog = () => {
    resetCheckoutForm();
    checkoutDialogVisible.value = true;
};

const handleReturn = (logId: number) => {
    ElMessageBox.confirm('确认此资源已经归还吗?', '确认归还', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info',
    }).then(() => {
        const logIndex = resourceLogs.value.findIndex(l => l.logId === logId);
        if (logIndex === -1) return;

        const log = resourceLogs.value[logIndex];
        log.status = '已归还';
        log.actualReturnTime = new Date().toLocaleString('sv-SE');

        const resource = allResources.value.find(r => r.resourceId === log.resourceId);
        if (resource) {
            resource.status = 2; // 2: 闲置
        }
        
        ElMessage.success('归还成功');
        console.log('资源归还成功:', JSON.parse(JSON.stringify(log)));
        console.log('资源状态更新:', JSON.parse(JSON.stringify(resource)));
    });
};

const submitCheckoutForm = async () => {
    if(!checkoutFormRef.value) return;
    await checkoutFormRef.value.validate((valid) => {
        if(valid) {
            const newLog: ResourceLog = {
                ...checkoutForm,
                logId: Math.max(...resourceLogs.value.map(l => l.logId), 0) + 1,
                checkoutTime: new Date().toLocaleString('sv-SE'),
                actualReturnTime: null,
                status: '已领用',
            };
            resourceLogs.value.unshift(newLog);

            const resource = allResources.value.find(r => r.resourceId === newLog.resourceId);
            if (resource) {
                resource.status = 1; // 1: 在用
            }

            ElMessage.success('领用登记成功');
            console.log('领用登记成功:', JSON.parse(JSON.stringify(newLog)));
            console.log('资源状态更新:', JSON.parse(JSON.stringify(resource)));
            checkoutDialogVisible.value = false;
        }
    });
};
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}
:deep(.el-card__body) {
  flex: 1;
  overflow: hidden;
}
.el-select {
    width: 100%;
}
</style> 