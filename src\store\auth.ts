import { reactive } from 'vue';
import type { User } from '@/types/user';

interface AuthState {
  user: User | null;
  token: string | null;
}

/**
 * @description 从 localStorage 初始化认证状态
 */
const state: AuthState = reactive({
  user: JSON.parse(localStorage.getItem('user') || 'null'),
  token: localStorage.getItem('token')
});

/**
 * @description 设置认证信息并存入 localStorage
 * @param {User} user - 用户信息
 * @param {string} token - 用户令牌
 */
function setAuth(user: User, token: string) {
  state.user = user;
  state.token = token;
  localStorage.setItem('user', JSON.stringify(user));
  localStorage.setItem('token', token);
}

/**
 * @description 清除认证信息和 localStorage
 */
function clearAuth() {
  state.user = null;
  state.token = null;
  localStorage.removeItem('user');
  localStorage.removeItem('token');
}

export const authStore = {
  state,
  setAuth,
  clearAuth
}; 