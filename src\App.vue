<script setup lang="ts">
// 家政服务管理系统 - 整合版本
// 支持管理员、家政人员、客户三种角色
</script>

<template>
  <div id="app">
    <router-view />
  </div>
</template>

<style>
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #f5f7fa;
  color: #1f2937;
}

#app {
  min-height: 100vh;
}

/* Element Plus 样式覆盖 */
.el-button--primary {
  background: linear-gradient(135deg, #4f46e5, #06b6d4);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #3730a3, #0891b2);
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
