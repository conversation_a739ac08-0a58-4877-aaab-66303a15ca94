// src/api/mockData/user.ts

// 模拟管理员列表
export const mockAdminList = [
  {
    adminId: 1,
    userId: 1,
    realName: '张经理',
    position: '运营经理',
    joinTime: '2022-01-10 00:00:00',
    avatar: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
  },
  {
    adminId: 2,
    userId: 2,
    realName: '李客服',
    position: '客服主管',
    joinTime: '2022-03-15 00:00:00',
    avatar: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
  },
  {
    adminId: 3,
    userId: 3,
    realName: '王人事',
    position: '人事专员',
    joinTime: '2023-05-20 00:00:00',
    avatar: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
  },
  {
    adminId: 4,
    userId: 4,
    realName: '赵运维',
    position: '运维工程师',
    joinTime: '2023-07-01 00:00:00',
    avatar: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
  },
  {
    adminId: 5,
    userId: 5,
    realName: '刘总',
    position: '超级管理员',
    joinTime: '2021-11-01 00:00:00',
    avatar: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
  }
];

// 模拟家政人员列表
export const mockCleanerList = [
  {
    cleanerId: 1,
    userId: 6,
    realName: '王芳',
    phone: '13400134006',
    idCard: '*************',
    gender: 0,
    age: 34,
    education: '高中',
    workYears: 5,
    qualification: JSON.stringify(['健康证A', '高级']),
    skills: JSON.stringify(['日常保洁']),
    status: 0,
    rating: 4.8,
    avatar: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
  },
  {
    cleanerId: 2,
    userId: 7,
    realName: '李秀英',
    phone: '13300133007',
    idCard: '*************',
    gender: 0,
    age: 36,
    education: '初中',
    workYears: 8,
    qualification: JSON.stringify(['健康证B']),
    skills: JSON.stringify(['家电清洗']),
    status: 1,
    rating: 4.9,
    avatar: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
  },
  {
    cleanerId: 3,
    userId: 8,
    realName: '张伟',
    phone: '13700137003',
    idCard: '*************',
    gender: 1,
    age: 32,
    education: '大专',
    workYears: 4,
    qualification: JSON.stringify(['电工证']),
    skills: JSON.stringify(['家电维修']),
    status: 1,
    rating: 4.7,
    avatar: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
  },
  {
    cleanerId: 4,
    userId: 9,
    realName: '陈静',
    phone: '13600136004',
    idCard: '*************',
    gender: 0,
    age: 29,
    education: '本科',
    workYears: 3,
    qualification: JSON.stringify(['育婴师证']),
    skills: JSON.stringify(['育儿陪护']),
    status: 0,
    rating: 5.0,
    avatar: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
  },
  {
    cleanerId: 5,
    userId: 10,
    realName: '赵强',
    phone: '13500135005',
    idCard: '*************',
    gender: 1,
    age: 39,
    education: '高中',
    workYears: 10,
    qualification: JSON.stringify(['健康证C', '厨师']),
    skills: JSON.stringify(['家庭烹饪']),
    status: 1,
    rating: 4.8,
    avatar: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
  }
];

// 模拟客户列表
export const mockCustomerList = [
  {
    customerId: 1,
    userId: 11,
    realName: '艾美丽',
    idCard: null,
    address: '广东省广州市天河',
    preference: JSON.stringify({"pet": "猫", "service_time": "工作日"}),
    vipLevel: 3,
    avatar: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
  },
  {
    customerId: 2,
    userId: 12,
    realName: '鲍勃',
    idCard: null,
    address: '广东省广州市越秀',
    preference: JSON.stringify({"cleaning_time": "周末"}),
    vipLevel: 2,
    avatar: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
  },
  {
    customerId: 3,
    userId: 13,
    realName: '伽马',
    idCard: null,
    address: '广东省广州市海珠',
    preference: JSON.stringify({"focus_area": "厨房"}),
    vipLevel: 4,
    avatar: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
  },
  {
    customerId: 4,
    userId: 14,
    realName: '希河洛',
    idCard: null,
    address: '广东省广州市白云',
    preference: JSON.stringify({"pet": "狗"}),
    vipLevel: 1,
    avatar: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
  },
  {
    customerId: 5,
    userId: 15,
    realName: '欧米茄',
    idCard: null,
    address: '广东省广州市番禺',
    preference: JSON.stringify({"special_need": "使用环保清洁剂"}),
    vipLevel: 5,
    avatar: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
  }
]; 