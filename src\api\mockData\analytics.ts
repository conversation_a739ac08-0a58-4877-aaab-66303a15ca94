export interface VipLevelDistribution {
  value: number;
  name: string;
}

export interface MonthlyNewCustomers {
  months: string[];
  counts: number[];
}

export const mockVipLevelDistribution: VipLevelDistribution[] = [
  { value: 120, name: 'VIP 1级' },
  { value: 85, name: 'VIP 2级' },
  { value: 60, name: 'VIP 3级' },
  { value: 35, name: 'VIP 4级' },
  { value: 15, name: 'VIP 5级' },
  { value: 250, name: '非VIP' },
];

export const mockMonthlyNewCustomers: MonthlyNewCustomers = {
  months: ['2024-01', '2024-02', '2024-03', '2024-04', '2024-05', '2024-06'],
  counts: [45, 60, 88, 72, 110, 95],
}; 