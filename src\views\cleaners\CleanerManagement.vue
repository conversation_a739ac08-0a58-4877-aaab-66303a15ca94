<template>
  <div class="cleaner-management">
    <div class="actions">
      <el-button type="primary" @click="openForm()">新增家政人员</el-button>
    </div>
    <el-table :data="cleaners" stripe style="width: 100%">
      <el-table-column prop="realName" label="姓名"></el-table-column>
      <el-table-column prop="age" label="年龄"></el-table-column>
      <el-table-column prop="gender" label="性别">
        <template #default="scope">
          {{ scope.row.gender === 1 ? '男' : '女' }}
        </template>
      </el-table-column>
      <el-table-column prop="workYears" label="工作年限"></el-table-column>
      <el-table-column prop="education" label="学历"></el-table-column>
      <el-table-column prop="skills" label="技能">
        <template #default="scope">
          <el-tag v-for="skill in scope.row.skills" :key="skill" style="margin-right: 5px;">{{ skill }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="工作状态">
        <template #default="scope">
          <el-tag :type="scope.row.status === 0 ? 'success' : 'warning'">
            {{ scope.row.status === 0 ? '空闲' : '忙碌' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="rating" label="评分"></el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button size="small" @click="openForm(scope.row)">编辑</el-button>
          <el-popconfirm title="确定删除吗？" @confirm="handleDelete(scope.row.id)">
            <template #reference>
              <el-button size="small" type="danger">删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog v-model="dialogVisible" :title="formTitle" width="50%">
      <CleanerForm v-if="dialogVisible" :cleaner="selectedCleaner" @close="closeDialog" />
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { getCleaners, deleteCleaner } from '../../api/user';
import type { Cleaner } from '../../types/user';
import CleanerForm from '../../components/cleaners/CleanerForm.vue';

const cleaners = ref<Cleaner[]>([]);
const dialogVisible = ref(false);
const selectedCleaner = ref<Cleaner | undefined>(undefined);
const formTitle = ref('');

const fetchCleaners = async () => {
  try {
    const response = await getCleaners();
    // The backend API doc shows pagination, but for now I'll assume it returns a list.
    // If it's paginated, this will need adjustment.
    cleaners.value = response.list || response;
  } catch (error) {
    ElMessage.error('获取家政人员列表失败');
  }
};

onMounted(fetchCleaners);

const openForm = (cleaner?: Cleaner) => {
  if (cleaner) {
    selectedCleaner.value = { ...cleaner };
    formTitle.value = '编辑家政人员';
  } else {
    selectedCleaner.value = undefined;
    formTitle.value = '新增家政人员';
  }
  dialogVisible.value = true;
};

const closeDialog = (refresh = false) => {
  dialogVisible.value = false;
  if (refresh) {
    fetchCleaners();
  }
};

const handleDelete = async (id: number) => {
  try {
    await deleteCleaner(id);
    ElMessage.success('删除成功');
    fetchCleaners();
  } catch (error) {
    ElMessage.error('删除失败');
  }
};
</script>

<style scoped>
.cleaner-management {
  padding: 20px;
}
.actions {
  margin-bottom: 20px;
}
</style> 