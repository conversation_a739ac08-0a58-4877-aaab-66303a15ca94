import { createRouter, createWebHistory } from 'vue-router'
import AdminLayout from '@/layout/AdminLayout.vue'
import CleanerLayout from '@/layout/CleanerLayout.vue'
import CustomerLayout from '@/layout/CustomerLayout.vue'
import Dashboard from '@/views/Dashboard.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    // 登录页面
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/Login.vue'),
      meta: { title: '登录' }
    },
    // 默认重定向到登录页
    {
      path: '/',
      redirect: '/login'
    },
    // 管理员后台
    {
      path: '/admin',
      component: AdminLayout,
      redirect: '/admin/dashboard',
      meta: { requiresAuth: true, role: 'admin' },
      children: [
        {
          path: 'dashboard',
          name: 'AdminDashboard',
          component: Dashboard,
          meta: { title: '仪表盘', icon: 'House' }
        },
        {
          path: 'user',
          name: 'User',
          redirect: '/user/admin',
          meta: { title: '用户管理', icon: 'User' },
          children: [
            {
              path: 'admin',
              name: 'AdminList',
              component: () => import('@/views/user/AdminList.vue'),
              meta: { title: '管理员列表' }
            },
            {
              path: 'cleaner',
              name: 'CleanerList',
              component: () => import('@/views/user/CleanerList.vue'),
              meta: { title: '家政员列表' }
            },
            {
              path: 'customer',
              name: 'CustomerList',
              component: () => import('@/views/user/CustomerList.vue'),
              meta: { title: '客户列表' }
            },
            {
              path: 'analytics',
              name: 'CustomerAnalytics',
              component: () => import('@/views/user/CustomerAnalytics.vue'),
              meta: { title: '客户分析' }
            }
          ]
        },
        {
          path: 'service',
          name: 'Service',
          redirect: '/service/list',
          meta: { title: '服务管理', icon: 'Goods' },
          children: [
            {
              path: 'list',
              name: 'ServiceList',
              component: () => import('@/views/service/ServiceList.vue'),
              meta: { title: '服务列表' }
            },
            {
              path: 'price',
              name: 'PriceList',
              component: () => import('@/views/service/PriceList.vue'),
              meta: { title: '价格管理' }
            },
          ]
        },
        {
          path: 'feedback',
          name: 'Feedback',
          redirect: '/feedback/list',
          meta: { title: '客户反馈', icon: 'ChatDotSquare' },
          children: [
            {
              path: 'list',
              name: 'FeedbackList',
              component: () => import('@/views/feedback/FeedbackList.vue'),
              meta: { title: '评价管理' }
            },
            {
              path: 'complaint',
              name: 'ComplaintList',
              component: () => import('@/views/feedback/ComplaintList.vue'),
              meta: { title: '投诉管理' }
            },
            {
              path: 'record',
              name: 'ServiceRecordList',
              component: () => import('@/views/feedback/ServiceRecordList.vue'),
              meta: { title: '服务记录' }
            }
          ]
        },
        {
          path: 'system',
          name: 'System',
          redirect: '/system/statistics',
          meta: { title: '系统管理', icon: 'Setting' },
          children: [
            {
              path: 'statistics',
              name: 'Statistics',
              component: () => import('@/views/system/Statistics.vue'),
              meta: { title: '运营统计' }
            },
            {
              path: 'notices',
              name: 'NoticeList',
              component: () => import('@/views/system/NoticeList.vue'),
              meta: { title: '公告管理' }
            }
          ]
        },
        {
          path: 'booking',
          name: 'Booking',
          redirect: '/booking/list',
          meta: { title: '预约管理', icon: 'Calendar' },
          children: [
            {
              path: 'list',
              name: 'BookingList',
              component: () => import('@/views/booking/BookingList.vue'),
              meta: { title: '预约列表' }
            }
          ]
        },
        {
          path: 'training',
          name: 'Training',
          redirect: '/training/list',
          meta: { title: '培训管理', icon: 'Finished' },
          children: [
            {
              path: 'list',
              name: 'TrainingList',
              component: () => import('@/views/training/TrainingList.vue'),
              meta: { title: '培训列表' }
            },
            {
              path: 'registration',
              name: 'RegistrationList',
              component: () => import('@/views/training/RegistrationList.vue'),
              meta: { title: '报名管理' }
            }
          ]
        },
        {
          path: 'resource',
          name: 'Resource',
          redirect: '/resource/list',
          meta: { title: '资源管理', icon: 'OfficeBuilding' },
          children: [
            {
              path: 'list',
              name: 'ResourceList',
              component: () => import('@/views/resource/ResourceList.vue'),
              meta: { title: '资源列表' }
            },
            {
              path: 'log',
              name: 'ResourceLog',
              component: () => import('@/views/resource/ResourceLog.vue'),
              meta: { title: '领用记录' }
            }
          ]
        },
      ]
    },
    // 家政人员端
    {
      path: '/cleaner',
      component: CleanerLayout,
      redirect: '/cleaner/dashboard',
      meta: { requiresAuth: true, role: 'cleaner' },
      children: [
        {
          path: 'dashboard',
          name: 'CleanerDashboard',
          component: () => import('@/views/cleaner/CleanerDashboard.vue'),
          meta: { title: '工作台' }
        },
        {
          path: 'tasks',
          name: 'CleanerTasks',
          component: () => import('@/views/cleaner/CleanerTasks.vue'),
          meta: { title: '我的任务' }
        },
        {
          path: 'profile',
          name: 'CleanerProfile',
          component: () => import('@/views/cleaner/CleanerProfile.vue'),
          meta: { title: '个人资料' }
        },
        {
          path: 'training',
          name: 'CleanerTraining',
          component: () => import('@/views/cleaner/CleanerTraining.vue'),
          meta: { title: '培训中心' }
        },
        {
          path: 'records',
          name: 'CleanerRecords',
          component: () => import('@/views/cleaner/CleanerRecords.vue'),
          meta: { title: '服务记录' }
        },
        {
          path: 'feedback',
          name: 'CleanerFeedback',
          component: () => import('@/views/cleaner/CleanerFeedback.vue'),
          meta: { title: '客户评价' }
        },
        {
          path: 'notices',
          name: 'CleanerNotices',
          component: () => import('@/views/cleaner/CleanerNotices.vue'),
          meta: { title: '公司公告' }
        },
        {
          path: 'report',
          name: 'CleanerReport',
          component: () => import('@/views/cleaner/CleanerReport.vue'),
          meta: { title: '工作报告' }
        }
      ]
    },
    // 客户端
    {
      path: '/customer',
      component: CustomerLayout,
      redirect: '/customer/dashboard',
      meta: { requiresAuth: true, role: 'customer' },
      children: [
        {
          path: 'dashboard',
          name: 'CustomerDashboard',
          component: () => import('@/views/customer/CustomerDashboard.vue'),
          meta: { title: '首页' }
        },
        {
          path: 'services',
          name: 'CustomerServices',
          component: () => import('@/views/customer/CustomerServices.vue'),
          meta: { title: '服务预约' }
        },
        {
          path: 'orders',
          name: 'CustomerOrders',
          component: () => import('@/views/customer/CustomerOrders.vue'),
          meta: { title: '我的订单' }
        },
        {
          path: 'cleaners',
          name: 'CustomerCleaners',
          component: () => import('@/views/customer/CustomerCleaners.vue'),
          meta: { title: '家政人员' }
        },
        {
          path: 'feedback',
          name: 'CustomerFeedback',
          component: () => import('@/views/customer/CustomerFeedback.vue'),
          meta: { title: '评价反馈' }
        },
        {
          path: 'profile',
          name: 'CustomerProfile',
          component: () => import('@/views/customer/CustomerProfile.vue'),
          meta: { title: '个人中心' }
        },
        {
          path: 'settings',
          name: 'CustomerSettings',
          component: () => import('@/views/customer/CustomerSettings.vue'),
          meta: { title: '账户设置' }
        }
      ]
    },
    {
      path: '/:pathMatch(.*)*',
      redirect: '/login'
    }
  ]
})

export default router 