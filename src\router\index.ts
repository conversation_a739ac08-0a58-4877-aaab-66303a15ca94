import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router';
import Login from '../views/Login.vue';
import Register from '../views/Register.vue';
import AdminDashboard from '../views/dashboards/AdminDashboard.vue';
import AdminHome from '../views/dashboards/AdminHome.vue';
import CleanerDashboard from '../views/dashboards/CleanerDashboard.vue';
import CleanerTasks from '../views/cleaners/CleanerTasks.vue';
import CleanerProfile from '../views/cleaners/CleanerProfile.vue';
import TrainingCenter from '../views/cleaners/TrainingCenter.vue';
import ServiceRecords from '../views/cleaners/ServiceRecords.vue';
import CustomerFeedback from '../views/cleaners/CustomerFeedback.vue';
import CompanyNotices from '../views/cleaners/CompanyNotices.vue';
import WorkReport from '../views/cleaners/WorkReport.vue';
import CustomerDashboard from '../views/dashboards/CustomerDashboard.vue';
import CleanerManagement from '../views/cleaners/CleanerManagement.vue';

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    redirect: '/login',
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
  },
  {
    path: '/admin',
    name: 'AdminDashboard',
    component: AdminDashboard,
    redirect: '/admin/dashboard',
    // meta: { requiresAuth: true, role: 1 }
    children: [
      {
        path: 'dashboard',
        name: 'AdminHome',
        component: AdminHome,
      },
      {
        path: 'cleaners',
        name: 'CleanerManagement',
        component: CleanerManagement,
      }
    ]
  },
  {
    path: '/cleaner',
    name: 'CleanerDashboard',
    component: CleanerDashboard,
    redirect: '/cleaner/tasks',
    // meta: { requiresAuth: true, role: 2 }
    children: [
      {
        path: 'tasks',
        name: 'CleanerTasks',
        component: CleanerTasks,
      },
      {
        path: 'profile',
        name: 'CleanerProfile',
        component: CleanerProfile,
      },
      {
        path: 'training',
        name: 'TrainingCenter',
        component: TrainingCenter,
      },
      {
        path: 'records',
        name: 'ServiceRecords',
        component: ServiceRecords,
      },
      {
        path: 'feedback',
        name: 'CustomerFeedback',
        component: CustomerFeedback,
      },
      {
        path: 'notices',
        name: 'CompanyNotices',
        component: CompanyNotices,
      },
      {
        path: 'report',
        name: 'WorkReport',
        component: WorkReport,
      }
    ]
  },
  {
    path: '/customer/dashboard',
    name: 'CustomerDashboard',
    component: CustomerDashboard,
    // meta: { requiresAuth: true, role: 3 }
  },
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
});

// 路由守卫逻辑已移除，稍后将重新添加

export default router; 