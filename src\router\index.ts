import { createRouter, createWebHistory } from 'vue-router'
import AdminLayout from '@/layout/AdminLayout.vue'
import Dashboard from '@/views/Dashboard.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: AdminLayout,
      redirect: '/dashboard',
      children: [
        {
          path: 'dashboard',
          name: 'Dashboard',
          component: Dashboard,
          meta: { title: '仪表盘', icon: 'House' }
        },
        {
          path: 'user',
          name: 'User',
          redirect: '/user/admin',
          meta: { title: '用户管理', icon: 'User' },
          children: [
            {
              path: 'admin',
              name: 'AdminList',
              component: () => import('@/views/user/AdminList.vue'),
              meta: { title: '管理员列表' }
            },
            {
              path: 'cleaner',
              name: 'CleanerList',
              component: () => import('@/views/user/CleanerList.vue'),
              meta: { title: '家政员列表' }
            },
            {
              path: 'customer',
              name: 'CustomerList',
              component: () => import('@/views/user/CustomerList.vue'),
              meta: { title: '客户列表' }
            },
            {
              path: 'analytics',
              name: 'CustomerAnalytics',
              component: () => import('@/views/user/CustomerAnalytics.vue'),
              meta: { title: '客户分析' }
            }
          ]
        },
        {
          path: 'service',
          name: 'Service',
          redirect: '/service/list',
          meta: { title: '服务管理', icon: 'Goods' },
          children: [
            {
              path: 'list',
              name: 'ServiceList',
              component: () => import('@/views/service/ServiceList.vue'),
              meta: { title: '服务列表' }
            },
            {
              path: 'price',
              name: 'PriceList',
              component: () => import('@/views/service/PriceList.vue'),
              meta: { title: '价格管理' }
            },
          ]
        },
        {
          path: 'feedback',
          name: 'Feedback',
          redirect: '/feedback/list',
          meta: { title: '客户反馈', icon: 'ChatDotSquare' },
          children: [
            {
              path: 'list',
              name: 'FeedbackList',
              component: () => import('@/views/feedback/FeedbackList.vue'),
              meta: { title: '评价管理' }
            },
            {
              path: 'complaint',
              name: 'ComplaintList',
              component: () => import('@/views/feedback/ComplaintList.vue'),
              meta: { title: '投诉管理' }
            },
            {
              path: 'record',
              name: 'ServiceRecordList',
              component: () => import('@/views/feedback/ServiceRecordList.vue'),
              meta: { title: '服务记录' }
            }
          ]
        },
        {
          path: 'system',
          name: 'System',
          redirect: '/system/statistics',
          meta: { title: '系统管理', icon: 'Setting' },
          children: [
            {
              path: 'statistics',
              name: 'Statistics',
              component: () => import('@/views/system/Statistics.vue'),
              meta: { title: '运营统计' }
            },
            {
              path: 'notices',
              name: 'NoticeList',
              component: () => import('@/views/system/NoticeList.vue'),
              meta: { title: '公告管理' }
            }
          ]
        },
        {
          path: 'booking',
          name: 'Booking',
          redirect: '/booking/list',
          meta: { title: '预约管理', icon: 'Calendar' },
          children: [
            {
              path: 'list',
              name: 'BookingList',
              component: () => import('@/views/booking/BookingList.vue'),
              meta: { title: '预约列表' }
            }
          ]
        },
        {
          path: 'training',
          name: 'Training',
          redirect: '/training/list',
          meta: { title: '培训管理', icon: 'Finished' },
          children: [
            {
              path: 'list',
              name: 'TrainingList',
              component: () => import('@/views/training/TrainingList.vue'),
              meta: { title: '培训列表' }
            },
            {
              path: 'registration',
              name: 'RegistrationList',
              component: () => import('@/views/training/RegistrationList.vue'),
              meta: { title: '报名管理' }
            }
          ]
        },
        {
          path: 'resource',
          name: 'Resource',
          redirect: '/resource/list',
          meta: { title: '资源管理', icon: 'OfficeBuilding' },
          children: [
            {
              path: 'list',
              name: 'ResourceList',
              component: () => import('@/views/resource/ResourceList.vue'),
              meta: { title: '资源列表' }
            },
            {
              path: 'log',
              name: 'ResourceLog',
              component: () => import('@/views/resource/ResourceLog.vue'),
              meta: { title: '领用记录' }
            }
          ]
        },
      ]
    },
    {
      path: '/:pathMatch(.*)*',
      redirect: '/'
    }
  ]
})

export default router 