<template>
  <div>
    <div class="welcome-message">
      <p>欢迎您！</p>
      <p>这是您当前的任务列表。请按时完成服务。</p>
    </div>

    <div class="task-filters">
      <button 
        @click="filterBookings()" 
        :class="{ active: activeFilter === 'all' }">
        全部任务
      </button>
      <button 
        @click="filterBookings(1)" 
        :class="{ active: activeFilter === 1 }">
        待服务
      </button>
      <button 
        @click="filterBookings(3)" 
        :class="{ active: activeFilter === 3 }">
        已完成
      </button>
    </div>

    <div v-if="isLoading" class="loading-state">
      正在加载任务列表...
    </div>
    <div v-else-if="error" class="error-state">
      加载失败：{{ error }}
    </div>
    <div v-else-if="bookings.length === 0" class="empty-state">
      <p>太棒了！您当前没有待处理的任务。</p>
    </div>
    <div v-else class="booking-list">
      <div v-for="booking in bookings" :key="booking.bookingId" class="booking-card">
        <div class="card-header">
          <span class="service-name">{{ booking.serviceName }}</span>
          <span :class="['status-badge', getStatusClass(booking.status)]">
            {{ getStatusText(booking.status) }}
          </span>
        </div>
        <div class="card-body">
          <p><strong>客户:</strong> {{ booking.customerName }}</p>
          <p><strong>地址:</strong> {{ booking.address }}</p>
          <p><strong>时间:</strong> {{ booking.serviceDate }} {{ booking.serviceTime }}</p>
        </div>
        <div class="card-footer">
          <button class="action-btn" @click="showDetails(booking)">查看详情</button>
        </div>
      </div>
    </div>

    <el-dialog v-model="detailsVisible" title="任务详情" width="500px">
      <div v-if="selectedBooking" class="booking-details">
        <p><strong>服务项目:</strong> {{ selectedBooking.serviceName }}</p>
        <p><strong>客户姓名:</strong> {{ selectedBooking.customerName }}</p>
        <p><strong>联系电话:</strong> {{ selectedBooking.customerPhone || '暂无' }}</p>
        <p><strong>服务地址:</strong> {{ selectedBooking.address }}</p>
        <p><strong>服务时间:</strong> {{ selectedBooking.serviceDate }} {{ selectedBooking.serviceTime }}</p>
        <p><strong>订单状态:</strong> {{ getStatusText(selectedBooking.status) }}</p>
        <p><strong>备注:</strong> {{ selectedBooking.remarks || '无' }}</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailsVisible = false">关闭</el-button>
          <el-button 
            v-if="selectedBooking && selectedBooking.status === 1" 
            type="success" 
            @click="handleUpdateStatus(selectedBooking.bookingId, 2)">
            开始服务
          </el-button>
          <el-button 
            v-if="selectedBooking && selectedBooking.status === 2" 
            type="success" 
            @click="handleUpdateStatus(selectedBooking.bookingId, 3)">
            完成服务
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { getBookings, updateBookingStatus } from '../../api/booking';
import type { Booking } from '../../types/booking';

const bookings = ref<Booking[]>([]);
const isLoading = ref(true);
const error = ref<string | null>(null);
const activeFilter = ref<'all' | number>('all');
const detailsVisible = ref(false);
const selectedBooking = ref<Booking | null>(null);

const statusMap: { [key: number]: { text: string; class: string } } = {
  1: { text: '待服务', class: 'status-pending' },
  2: { text: '服务中', class: 'status-progress' },
  3: { text: '已完成', class: 'status-completed' },
  4: { text: '已取消', class: 'status-cancelled' },
};

const getStatusText = (status: number) => {
  return statusMap[status]?.text || '未知状态';
};

const getStatusClass = (status: number) => {
  return statusMap[status]?.class || 'status-unknown';
};

const showDetails = (booking: Booking) => {
  console.log('查看的预约详情:', booking);
  selectedBooking.value = booking;
  detailsVisible.value = true;
};

const handleUpdateStatus = async (bookingId: string | number, newStatus: number) => {
  try {
    await updateBookingStatus(bookingId, newStatus);
    ElMessage.success('状态更新成功（模拟）');
    detailsVisible.value = false;
    fetchBookings(activeFilter.value === 'all' ? undefined : activeFilter.value);
  } catch (err: any) {
    if (err && !err.__MOCK__) {
      ElMessage.error(err.message || '状态更新失败');
    }
  }
};

const fetchBookings = async (status?: number) => {
  isLoading.value = true;
  error.value = null;
  try {
    const params = status !== undefined ? { status } : {};
    const responseData = await getBookings(params);
    bookings.value = Array.isArray(responseData) ? responseData : responseData.list || [];
  } catch (err: any) {
    error.value = err.message || '无法连接到服务器';
  } finally {
    isLoading.value = false;
  }
};

const filterBookings = (status?: number) => {
  activeFilter.value = status === undefined ? 'all' : status;
  fetchBookings(status);
};

onMounted(() => {
  fetchBookings(1); 
  activeFilter.value = 1;
});
</script>

<style scoped>
/* 保持原有的样式 */
.task-filters {
  margin-bottom: 20px;
}
.task-filters button {
  margin-right: 10px;
  padding: 8px 15px;
  border: 1px solid #ddd;
  background-color: #fff;
  cursor: pointer;
  border-radius: 4px;
}
.task-filters button.active {
  background-color: #409eff;
  color: #fff;
  border-color: #409eff;
}
.booking-list {
  display: grid;
  gap: 20px;
}
.booking-card {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-weight: bold;
}
.status-badge {
  padding: 5px 10px;
  border-radius: 12px;
  font-size: 12px;
  color: #fff;
}
.status-pending { background-color: #f56c6c; }
.status-progress { background-color: #e6a23c; }
.status-completed { background-color: #67c23a; }
.status-cancelled { background-color: #909399; }
.card-footer {
  margin-top: 15px;
  text-align: right;
}
</style> 