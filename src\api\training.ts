import service from './index';
import type { TrainingQueryParams } from '../types/training';

/**
 * @description 获取培训列表
 * @param params 查询参数
 */
export const getTrainings = (params?: TrainingQueryParams): Promise<any> => {
  return service({
    url: '/trainings',
    method: 'get',
    params,
  });
};

/**
 * @description 员工报名参加培训
 * @param trainingId 培训ID
 * @param cleanerId 家政人员ID
 */
export const registerForTraining = (trainingId: number | string, cleanerId: number): Promise<any> => {
  return service({
    url: `/trainings/${trainingId}/register`,
    method: 'post',
    data: { cleanerId },
  });
};

/**
 * @description 员工取消培训报名
 * @param trainingId 培训ID
 * @param cleanerId 家政人员ID
 */
export const unregisterFromTraining = (trainingId: number | string, cleanerId: number): Promise<any> => {
  return service({
    url: `/trainings/${trainingId}/unregister`,
    method: 'delete',
    data: { cleanerId }, // DELETE请求通常在URL中传递参数，但遵循文档，我们将其放在data中
  });
}; 