<template>
  <div class="report-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>个人工作报告</span>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
            @change="generateReport"
          />
        </div>
      </template>

      <div v-if="isLoading" class="loading-state">正在生成报告...</div>
      <div v-else-if="reportData">
        <!-- Key Metrics -->
        <el-row :gutter="20" class="metrics-row">
          <el-col :span="8">
            <el-statistic title="完成单数" :value="reportData.totalBookings" />
          </el-col>
          <el-col :span="8">
            <el-statistic title="总工时 (小时)" :value="reportData.totalHours" />
          </el-col>
          <el-col :span="8">
            <el-statistic title="平均评分" :value="reportData.averageRating">
              <template #suffix><el-icon style="vertical-align: -0.125em"><StarFilled /></el-icon></template>
            </el-statistic>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <!-- Service Distribution Chart -->
          <el-col :span="12">
            <el-card shadow="never">
              <template #header>服务类型分布</template>
              <v-chart class="chart" :option="chartOption" autoresize />
            </el-card>
          </el-col>

          <!-- Recent Bookings -->
          <el-col :span="12">
            <el-card shadow="never">
              <template #header>近期完成的服务</template>
              <el-table :data="reportData.recentBookings" height="300px">
                <el-table-column prop="customerName" label="客户" />
                <el-table-column prop="serviceDate" label="服务日期" />
                <el-table-column prop="status" label="状态" />
              </el-table>
            </el-card>
          </el-col>
        </el-row>
      </div>
      <div v-else class="empty-state">
        <el-empty description="选择日期范围以生成报告" />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { PieChart } from 'echarts/charts';
import { TitleComponent, TooltipComponent, LegendComponent } from 'echarts/components';
import VChart from 'vue-echarts';
import { StarFilled } from '@element-plus/icons-vue';
import type { WorkReportData } from '../../types/statistics';

use([CanvasRenderer, PieChart, TitleComponent, TooltipComponent, LegendComponent]);

const dateRange = ref<[Date, Date] | null>(null);
const isLoading = ref(false);
const reportData = ref<WorkReportData | null>(null);

const pickerOptions = {
  shortcuts: [
    { text: '最近一周', onClick: (picker: any) => { /* ... */ } },
    { text: '最近一个月', onClick: (picker: any) => { /* ... */ } },
    { text: '最近三个月', onClick: (picker: any) => { /* ... */ } },
  ]
};

const generateReport = () => {
  if (!dateRange.value) return;
  isLoading.value = true;
  console.log(`生成报告，时间范围: ${dateRange.value[0]} - ${dateRange.value[1]}`);

  // 纯前端模拟
  setTimeout(() => {
    reportData.value = {
      totalBookings: 28,
      totalHours: 112,
      averageRating: 4.9,
      serviceTypeDistribution: [
        { name: '日常保洁', value: 15 },
        { name: '深度保洁', value: 8 },
        { name: '家电清洗', value: 3 },
        { name: '除螨服务', value: 2 },
      ],
      recentBookings: [
        { id: 101, customerName: '王女士', serviceDate: '2024-06-20', status: '已完成' },
        { id: 102, customerName: '李先生', serviceDate: '2024-06-18', status: '已完成' },
        { id: 103, customerName: '张阿姨', serviceDate: '2024-06-15', status: '已完成' },
        { id: 104, customerName: '赵先生', serviceDate: '2024-06-12', status: '已完成' },
        { id: 105, customerName: '周女士', serviceDate: '2024-06-10', status: '已完成' },
      ],
    };
    isLoading.value = false;
  }, 1000);
};

const chartOption = computed(() => ({
  tooltip: { trigger: 'item' },
  legend: { top: 'bottom' },
  series: [
    {
      name: '服务类型',
      type: 'pie',
      radius: '70%',
      center: ['50%', '45%'],
      data: reportData.value?.serviceTypeDistribution,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)',
        },
      },
    },
  ],
}));
</script>

<style scoped>
.report-container {
  padding: 20px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.metrics-row {
  margin-bottom: 20px;
}
.chart {
  height: 350px;
}
.loading-state, .empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}
</style> 