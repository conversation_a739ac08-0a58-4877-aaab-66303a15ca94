<template>
  <el-card class="page-card">
    <template #header>
      <div class="card-header">
        <span>管理员列表</span>
        <el-button class="button" type="primary" @click="handleAdd">新增管理员</el-button>
      </div>
    </template>
    
    <el-table :data="adminList" style="width: 100%" height="100%">
      <el-table-column prop="adminId" label="管理员ID" width="100" />
      <el-table-column prop="userId" label="用户ID" width="100" />
      <el-table-column label="头像" width="100">
        <template #default="scope">
          <el-avatar :src="scope.row.avatar" />
        </template>
      </el-table-column>
      <el-table-column prop="realName" label="真实姓名" width="150" />
      <el-table-column prop="position" label="职位" width="200" />
      <el-table-column prop="joinTime" label="入职时间" />
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-card>

  <el-dialog v-model="dialogVisible" :title="dialogTitle" width="500px">
    <el-form :model="adminForm" :rules="rules" ref="adminFormRef" label-width="80px">
      <el-form-item label="真实姓名" prop="realName">
        <el-input v-model="adminForm.realName" autocomplete="off" />
      </el-form-item>
      <el-form-item label="职位" prop="position">
        <el-input v-model="adminForm.position" autocomplete="off" />
      </el-form-item>
      <el-form-item label="头像" prop="avatar">
        <el-upload
          class="avatar-uploader"
          action="#"
          :show-file-list="false"
          :before-upload="beforeAvatarUpload"
          :http-request="() => {}"
        >
          <img v-if="adminForm.avatar" :src="adminForm.avatar" class="avatar" />
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
        </el-upload>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm(adminFormRef)">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive } from 'vue'
import { mockAdminList } from '@/api/mockData/user'
import type { FormInstance, FormRules, UploadProps } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

interface Admin {
  adminId: number;
  userId: number;
  realName: string;
  position: string;
  joinTime: string;
  avatar: string;
}

const adminList = ref<Admin[]>(mockAdminList)

const dialogVisible = ref(false)
const dialogTitle = ref('新增管理员')
const dialogMode = ref('add') // 'add' or 'edit'

const adminFormRef = ref<FormInstance>()
const adminForm = reactive<Omit<Admin, 'joinTime'>>({
  adminId: 0,
  userId: 0,
  realName: '',
  position: '',
  avatar: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
})

const rules = reactive<FormRules>({
  realName: [{ required: true, message: '请输入真实姓名', trigger: 'blur' }],
  position: [{ required: true, message: '请输入职位', trigger: 'blur' }],
  avatar: [{ required: true, message: '请上传头像', trigger: 'blur' }],
})

const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
  const isJpgOrPng = rawFile.type === 'image/jpeg' || rawFile.type === 'image/png';
  if (!isJpgOrPng) {
    ElMessage.error('头像只能是 JPG/PNG 格式!')
    return false
  } else if (rawFile.size / 1024 / 1024 > 2) {
    ElMessage.error('头像大小不能超过 2MB!')
    return false
  }
  adminForm.avatar = URL.createObjectURL(rawFile)
  return false // Prevent auto-upload
}

const handleAdd = () => {
  dialogTitle.value = '新增管理员'
  dialogMode.value = 'add'
  
  // Reset form
  Object.assign(adminForm, {
    adminId: 0,
    userId: 0,
    realName: '',
    position: '',
    avatar: ''
  })
  adminFormRef.value?.clearValidate()
  
  dialogVisible.value = true
}

const handleEdit = (row: Admin) => {
  dialogTitle.value = '编辑管理员'
  dialogMode.value = 'edit'
  
  // Deep copy to avoid reactive issues
  Object.assign(adminForm, JSON.parse(JSON.stringify(row)))
  
  dialogVisible.value = true
}

const handleDelete = (row: Admin) => {
  ElMessageBox.confirm(
    `确定要删除管理员 "${row.realName}" 吗?`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const index = adminList.value.findIndex(a => a.adminId === row.adminId)
    if (index !== -1) {
      adminList.value.splice(index, 1)
      ElMessage.success('删除成功')
      console.log('删除后，管理员列表数据:', JSON.parse(JSON.stringify(adminList.value)))
    }
  }).catch(() => {
    // User canceled
  })
}

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid) => {
    if (valid) {
      if (dialogMode.value === 'add') {
        const newAdmin: Admin = {
          ...adminForm,
          adminId: Math.max(...adminList.value.map(a => a.adminId)) + 1,
          userId: Math.max(...adminList.value.map(a => a.userId)) + 1,
          joinTime: new Date().toLocaleString('sv-SE'),
        }
        adminList.value.unshift(newAdmin)
        ElMessage.success('新增成功')
        console.log('新增后，管理员列表数据:', JSON.parse(JSON.stringify(adminList.value)))
      } else { // 'edit' mode
        const index = adminList.value.findIndex(a => a.adminId === adminForm.adminId)
        if (index !== -1) {
          // Keep original joinTime and userId
          const originalAdmin = adminList.value[index]
          adminList.value[index] = { 
            ...adminForm,
            joinTime: originalAdmin.joinTime,
            userId: originalAdmin.userId
          }
          ElMessage.success('更新成功')
          console.log('编辑后，管理员列表数据:', JSON.parse(JSON.stringify(adminList.value)))
        }
      }
      dialogVisible.value = false
    }
  })
}

onMounted(() => {
  console.log("页面加载时，管理员列表模拟数据:", JSON.parse(JSON.stringify(adminList.value)))
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}
:deep(.el-card__body) {
  flex: 1;
  overflow: hidden;
}
.avatar-uploader .avatar {
  width: 120px;
  height: 120px;
  display: block;
}
</style>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  text-align: center;
}
</style> 