<template>
  <el-card class="page-card">
    <template #header>
      <div class="card-header">
        <span>投诉列表</span>
      </div>
    </template>

    <el-table :data="complaintTableData" style="width: 100%" height="100%">
      <el-table-column prop="complaintId" label="ID" width="60" />
      <el-table-column prop="customerName" label="投诉客户" width="120" />
      <el-table-column prop="bookingId" label="关联预定ID" width="110">
        <template #default="scope">
          {{ scope.row.bookingId || '无' }}
        </template>
      </el-table-column>
      <el-table-column prop="complaintType" label="投诉类型" width="120" />
      <el-table-column prop="complaintDesc" label="投诉详情" show-overflow-tooltip />
      <el-table-column prop="complaintTime" label="投诉时间" width="180" />
      <el-table-column prop="status" label="状态" width="120">
        <template #default="scope">
          <el-tag :type="statusMap[scope.row.status].type">
            {{ statusMap[scope.row.status].text }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="solution" label="解决方案" show-overflow-tooltip>
         <template #default="scope">
          {{ scope.row.solution || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="adminName" label="处理人" width="120">
         <template #default="scope">
          {{ scope.row.adminName || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100" fixed="right">
        <template #default="scope">
          <el-button size="small" type="danger" @click="handleProcess(scope.row)">
            处理
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-card>

  <el-dialog v-model="dialogVisible" title="处理投诉" width="600px">
    <el-form :model="processForm" ref="processFormRef" label-width="100px" >
       <el-form-item label="投诉详情">
         <el-input v-model="currentComplaintDesc" type="textarea" :rows="4" disabled />
       </el-form-item>
       <el-form-item label="处理状态" prop="status">
        <el-select v-model="processForm.status" placeholder="请选择处理状态">
          <el-option v-for="(item, key) in statusMap" :key="key" :label="item.text" :value="Number(key)" />
        </el-select>
       </el-form-item>
       <el-form-item label="解决方案" prop="solution">
        <el-input v-model="processForm.solution" type="textarea" :rows="4" placeholder="请输入解决方案"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitProcess">提交</el-button>
      </span>
    </template>
  </el-dialog>

</template>

<script lang="ts" setup>
import { ref, onMounted, reactive, computed } from 'vue';
import { mockComplaintList, type Complaint } from '@/api/mockData/feedback';
import { mockCustomerList, mockAdminList } from '@/api/mockData/user';
import type { FormInstance } from 'element-plus';
import { ElMessage } from 'element-plus';

const complaintList = ref<Complaint[]>(JSON.parse(JSON.stringify(mockComplaintList)));
const dialogVisible = ref(false);
const processFormRef = ref<FormInstance>();
const processForm = reactive({
  complaintId: null,
  status: 0,
  solution: '',
});
const currentComplaintDesc = ref('');

const statusMap: { [key: number]: { text: string; type: 'warning' | 'primary' | 'success' } } = {
  0: { text: '待处理', type: 'warning' },
  1: { text: '处理中', type: 'primary' },
  2: { text: '已解决', type: 'success' },
};

const customerNameMap = computed(() => {
  const map = new Map<number, string>();
  mockCustomerList.forEach(c => map.set(c.customerId, c.realName));
  return map;
});

const adminNameMap = computed(() => {
  const map = new Map<number, string>();
  mockAdminList.forEach(a => map.set(a.adminId, a.realName));
  return map;
});

const complaintTableData = computed(() => {
  return complaintList.value.map(c => ({
    ...c,
    customerName: customerNameMap.value.get(c.customerId) || `客户ID:${c.customerId}`,
    adminName: adminNameMap.value.get(c.adminId as number) || (c.adminId ? `管理员ID:${c.adminId}` : '-'),
  }));
});

const handleProcess = (row: any) => {
  processForm.complaintId = row.complaintId;
  processForm.status = row.status;
  processForm.solution = row.solution || '';
  currentComplaintDesc.value = row.complaintDesc;
  dialogVisible.value = true;
};

const submitProcess = async () => {
  if (!processFormRef.value) return;
  await processFormRef.value.validate((valid) => {
    if(valid) {
      const index = complaintList.value.findIndex(c => c.complaintId === processForm.complaintId);
      if(index !== -1) {
        complaintList.value[index].status = processForm.status;
        complaintList.value[index].solution = processForm.solution;
        
        // 从现有管理员列表中随机选择一个进行指派，模拟真实操作
        const availableAdmins = mockAdminList.filter(a => a.adminId);
        if (availableAdmins.length > 0) {
            const randomAdmin = availableAdmins[Math.floor(Math.random() * availableAdmins.length)];
            complaintList.value[index].adminId = randomAdmin.adminId;
        } else {
            complaintList.value[index].adminId = 1; // Fallback if no admins found
        }
        
        ElMessage.success('处理成功');
        console.log('处理后，投诉列表数据:', JSON.parse(JSON.stringify(complaintList.value)));
      }
      dialogVisible.value = false;
    }
  });
};

onMounted(() => {
  console.log('页面加载时，投诉列表数据:', JSON.parse(JSON.stringify(complaintList.value)));
});

</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}
:deep(.el-card__body) {
  flex: 1;
  overflow: hidden;
}
</style>