<template>
  <div class="auth-page">
    <div class="auth-container">
      <!-- Logo和标题区域 -->
      <div class="auth-header">
        <div class="logo-container">
          <div class="logo-icon">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M9 22V12H15V22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <h1 class="logo-text">家政服务管理平台</h1>
        </div>
        <p class="auth-subtitle">欢迎登录，请选择您的身份</p>
      </div>

      <!-- 登录表单 -->
      <el-form @submit.prevent="handleLogin" class="auth-form" :model="loginForm" :rules="rules" ref="formRef">
        <el-form-item prop="username">
          <el-input 
            v-model="loginForm.username" 
            placeholder="请输入用户名"
            size="large"
            :prefix-icon="User"
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input 
            v-model="loginForm.password" 
            type="password" 
            placeholder="请输入密码"
            size="large"
            :prefix-icon="Lock"
            show-password
          />
        </el-form-item>

        <el-form-item prop="userType">
          <el-select 
            v-model="loginForm.userType" 
            placeholder="请选择登录身份"
            size="large"
            style="width: 100%"
          >
            <el-option label="管理员" :value="1">
              <div class="role-option">
                <el-icon><Setting /></el-icon>
                <span>管理员</span>
              </div>
            </el-option>
            <el-option label="家政人员" :value="2">
              <div class="role-option">
                <el-icon><User /></el-icon>
                <span>家政人员</span>
              </div>
            </el-option>
            <el-option label="客户" :value="3">
              <div class="role-option">
                <el-icon><UserFilled /></el-icon>
                <span>客户</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 登录按钮 -->
        <el-form-item>
          <el-button 
            type="primary" 
            size="large" 
            style="width: 100%" 
            :loading="isLoading"
            @click="handleLogin"
          >
            {{ isLoading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 底部链接 -->
      <div class="auth-footer">
        <p class="auth-link">
          还没有账户？ 
          <router-link to="/register" class="link-primary">立即注册</router-link>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import { User, Lock, Setting, UserFilled } from '@element-plus/icons-vue';

const router = useRouter();
const formRef = ref<FormInstance>();
const isLoading = ref(false);

const loginForm = reactive({
  username: '',
  password: '',
  userType: null as number | null
});

const rules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  userType: [
    { required: true, message: '请选择登录身份', trigger: 'change' }
  ]
};

const handleLogin = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    isLoading.value = true;

    // 模拟登录请求
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 根据用户类型跳转到不同的页面
    switch (loginForm.userType) {
      case 1: // 管理员
        ElMessage.success('管理员登录成功');
        router.push('/admin');
        break;
      case 2: // 家政人员
        ElMessage.success('家政人员登录成功');
        router.push('/cleaner');
        break;
      case 3: // 客户
        ElMessage.success('客户登录成功');
        router.push('/customer');
        break;
      default:
        ElMessage.error('未知的用户类型');
        break;
    }
  } catch (error) {
    console.error('登录失败:', error);
  } finally {
    isLoading.value = false;
  }
};
</script>

<style scoped>
.auth-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.auth-container {
  width: 100%;
  max-width: 420px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
}

.auth-header {
  text-align: center;
  margin-bottom: 30px;
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 15px;
}

.logo-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
}

.logo-icon svg {
  width: 32px;
  height: 32px;
  color: white;
}

.logo-text {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin: 0;
}

.auth-subtitle {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.auth-form {
  margin-bottom: 20px;
}

.role-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.auth-footer {
  text-align: center;
}

.auth-link {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.link-primary {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
}

.link-primary:hover {
  text-decoration: underline;
}

@media (max-width: 480px) {
  .auth-container {
    padding: 30px 20px;
    margin: 10px;
  }
}
</style>
