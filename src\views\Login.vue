<template>
  <div class="auth-container">
    <div class="logo">家政服务</div>
    <h2>登录</h2>
    <form @submit.prevent="handleLogin">
      <div class="form-group">
        <label for="username">用户名</label>
        <input type="text" id="username" v-model="username" required />
      </div>
      <div class="form-group">
        <label for="password">密码</label>
        <input type="password" id="password" v-model="password" required />
      </div>
      <div class="form-group">
        <button type="submit">登录</button>
      </div>
    </form>
    <p>
      还没有账户？ <router-link to="/register">立即注册</router-link>
    </p>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { login } from '../api/auth';
import type { UserLogin } from '../types/user';

const username = ref('');
const password = ref('');
const router = useRouter();

/**
 * @description 处理登录逻辑
 */
const handleLogin = async () => {
  try {
    const credentials: UserLogin = {
      username: username.value,
      password: password.value,
    };

    const responseData = await login(credentials);

    // 后端返回的 userType 是字符串，需要先转换为数字再进行比较
    const userType = parseInt(responseData.userType, 10);

    // 根据用户角色重定向
    switch (userType) {
      case 1: // 管理员
        router.push('/admin/dashboard');
        break;
      case 2: // 家政人员
        router.push('/cleaner');
        break;
      case 3: // 客户
        router.push('/customer/dashboard');
        break;
      default:
        alert('登录成功，但用户角色未知！');
        router.push('/login');
        break;
    }
  } catch (error: any) {
    console.error('Login failed', error);
    alert(`登录失败: ${error.message}`);
  }
};
</script>

<style scoped>
/* Scoped styles are applied via global styles in App.vue */
</style> 