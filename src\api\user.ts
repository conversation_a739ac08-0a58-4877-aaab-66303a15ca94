import service from './index';
import type { Cleaner } from '../types/user';

/**
 * @description 获取家政人员列表
 * @param params 查询参数
 */
export const getCleaners = (params?: any): Promise<any> => {
    return service({
        url: '/users/cleaners',
        method: 'get',
        params,
    });
};

/**
 * @description 获取家政人员详情
 * @param cleanerId 家政人员ID
 */
export const getCleanerById = (cleanerId: number): Promise<any> => {
    return service({
        url: `/users/cleaners/${cleanerId}`,
        method: 'get',
    });
};


/**
 * @description 创建家政人员
 * @param data 家政人员信息
 */
export const createCleaner = (data: Cleaner): Promise<any> => {
    return service({
        url: '/users/cleaners',
        method: 'post',
        data,
    });
};

/**
 * @description 更新家政人员信息
 * @param cleanerId 家政人员ID
 * @param data 家政人员信息
 */
export const updateCleaner = (cleanerId: number, data: Partial<Cleaner>): Promise<any> => {
    return service({
        url: `/users/cleaners/${cleanerId}`,
        method: 'put',
        data,
    });
};

/**
 * @description 删除家政人员
 * @param cleanerId 家政人员ID
 */
export const deleteCleaner = (cleanerId: number): Promise<any> => {
    return service({
        url: `/users/cleaners/${cleanerId}`,
        method: 'delete',
    });
}; 