<template>
  <div class="admin-dashboard">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <h1 class="welcome-title">欢迎回来，管理员</h1>
        <p class="welcome-subtitle">今天是 {{ currentDate }}，系统运行正常</p>
      </div>
      <div class="quick-stats">
        <div class="stat-item">
          <div class="stat-number">{{ totalUsers }}</div>
          <div class="stat-label">总用户数</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ todayOrders }}</div>
          <div class="stat-label">今日订单</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ activeCleaners }}</div>
          <div class="stat-label">在线家政员</div>
        </div>
      </div>
    </div>

    <!-- 数据概览 -->
    <div class="overview-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon users">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">1,234</div>
                <div class="stat-title">注册用户</div>
                <div class="stat-change positive">+12%</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon orders">
                <el-icon><List /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">856</div>
                <div class="stat-title">总订单数</div>
                <div class="stat-change positive">+8%</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon revenue">
                <el-icon><Money /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">¥45,678</div>
                <div class="stat-title">本月收入</div>
                <div class="stat-change positive">+15%</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon cleaners">
                <el-icon><Avatar /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">89</div>
                <div class="stat-title">家政人员</div>
                <div class="stat-change positive">+3%</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表和列表 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>订单趋势</span>
            </div>
          </template>
          <div class="chart-container">
            <div style="text-align: center; color: #999;">图表功能开发中...</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最新订单</span>
              <el-button text type="primary" @click="$router.push('/admin/booking/list')">查看全部</el-button>
            </div>
          </template>
          <div class="recent-orders">
            <div v-for="order in recentOrders" :key="order.id" class="order-item">
              <div class="order-info">
                <div class="order-title">{{ order.service }}</div>
                <div class="order-customer">{{ order.customer }}</div>
                <div class="order-time">{{ order.time }}</div>
              </div>
              <el-tag :type="getOrderStatusType(order.status)">{{ order.status }}</el-tag>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <h3>快速操作</h3>
      <el-row :gutter="16">
        <el-col :span="6">
          <el-button type="primary" @click="$router.push('/admin/user/admin')" style="width: 100%; height: 60px;">
            <div style="display: flex; flex-direction: column; align-items: center;">
              <el-icon style="font-size: 20px; margin-bottom: 4px;"><User /></el-icon>
              <span>用户管理</span>
            </div>
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="success" @click="$router.push('/admin/service/list')" style="width: 100%; height: 60px;">
            <div style="display: flex; flex-direction: column; align-items: center;">
              <el-icon style="font-size: 20px; margin-bottom: 4px;"><Goods /></el-icon>
              <span>服务管理</span>
            </div>
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="warning" @click="$router.push('/admin/booking/list')" style="width: 100%; height: 60px;">
            <div style="display: flex; flex-direction: column; align-items: center;">
              <el-icon style="font-size: 20px; margin-bottom: 4px;"><Calendar /></el-icon>
              <span>预约管理</span>
            </div>
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="info" @click="$router.push('/admin/system/statistics')" style="width: 100%; height: 60px;">
            <div style="display: flex; flex-direction: column; align-items: center;">
              <el-icon style="font-size: 20px; margin-bottom: 4px;"><Setting /></el-icon>
              <span>系统设置</span>
            </div>
          </el-button>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { User, List, Money, Avatar, Goods, Calendar, Setting } from '@element-plus/icons-vue';

// 响应式数据
const totalUsers = ref(1234);
const todayOrders = ref(45);
const activeCleaners = ref(23);

// 当前日期
const currentDate = computed(() => {
  return new Date().toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  });
});

// 最新订单数据
const recentOrders = ref([
  {
    id: 1,
    service: '家庭深度清洁',
    customer: '王女士',
    time: '2024-01-20 14:00',
    status: '进行中'
  },
  {
    id: 2,
    service: '日常保洁',
    customer: '李先生',
    time: '2024-01-20 16:00',
    status: '待服务'
  },
  {
    id: 3,
    service: '家电清洗',
    customer: '张女士',
    time: '2024-01-20 18:00',
    status: '已完成'
  },
  {
    id: 4,
    service: '开荒保洁',
    customer: '赵先生',
    time: '2024-01-21 09:00',
    status: '待服务'
  }
]);

// 获取订单状态类型
const getOrderStatusType = (status: string) => {
  switch (status) {
    case '待服务': return 'warning';
    case '进行中': return 'primary';
    case '已完成': return 'success';
    default: return 'info';
  }
};
</script>

<style scoped>
.admin-dashboard {
  padding: 0;
}

/* 欢迎区域 */
.welcome-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.welcome-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
}

.quick-stats {
  display: flex;
  gap: 30px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
}

/* 数据卡片 */
.stat-card {
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.stat-icon.users { background: #e3f2fd; color: #1976d2; }
.stat-icon.orders { background: #f3e5f5; color: #7b1fa2; }
.stat-icon.revenue { background: #e8f5e8; color: #388e3c; }
.stat-icon.cleaners { background: #fff3e0; color: #f57c00; }

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin-bottom: 4px;
}

.stat-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 12px;
  font-weight: 600;
}

.stat-change.positive {
  color: #4caf50;
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 最新订单 */
.recent-orders {
  max-height: 300px;
  overflow-y: auto;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.order-item:last-child {
  border-bottom: none;
}

.order-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.order-customer {
  font-size: 14px;
  color: #666;
  margin-bottom: 2px;
}

.order-time {
  font-size: 12px;
  color: #999;
}

/* 快速操作 */
.quick-actions {
  margin-top: 24px;
}

.quick-actions h3 {
  margin-bottom: 16px;
  color: #333;
  font-weight: 600;
}

/* 图表容器 */
.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 8px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-section {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .quick-stats {
    gap: 20px;
  }

  .overview-section .el-col {
    margin-bottom: 16px;
  }
}
</style>