<template>
  <el-card class="page-card">
    <template #header>
      <div class="card-header">
        <span>客户列表</span>
        <el-button class="button" type="primary" @click="handleAdd">新增客户</el-button>
      </div>
    </template>
    
    <el-table :data="customerList" style="width: 100%" height="100%">
      <el-table-column prop="customerId" label="客户ID" width="80" />
      <el-table-column prop="userId" label="用户ID" width="80" />
      <el-table-column label="头像" width="80">
        <template #default="scope">
          <el-avatar :src="scope.row.avatar" />
        </template>
      </el-table-column>
      <el-table-column prop="realName" label="真实姓名" width="120" />
      <el-table-column prop="address" label="家庭地址" />
      <el-table-column prop="preference" label="服务偏好">
         <template #default="scope">
          <el-tag v-for="(val, key) in JSON.parse(scope.row.preference)" :key="key" style="margin: 2px;">{{ key }}: {{ val }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="vipLevel" label="VIP等级" width="160">
         <template #default="scope">
          <el-rate v-model="scope.row.vipLevel" disabled />
        </template>
      </el-table-column>
       <el-table-column label="操作" width="150">
        <template #default="scope">
          <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-card>

  <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px" :close-on-click-modal="false">
    <el-form :model="customerForm" :rules="rules" ref="customerFormRef" label-width="80px">
      <el-form-item label="真实姓名" prop="realName">
        <el-input v-model="customerForm.realName" />
      </el-form-item>
       <el-form-item label="家庭地址" prop="address">
        <el-input v-model="customerForm.address" />
      </el-form-item>
       <el-form-item label="VIP等级" prop="vipLevel">
        <el-rate v-model="customerForm.vipLevel" :max="5" show-score />
      </el-form-item>
      <el-form-item label="服务偏好">
        <div v-for="(item, index) in customerForm.preferenceItems" :key="index" style="display: flex; margin-bottom: 10px; align-items: center;">
          <el-input v-model="item.key" placeholder="偏好名" style="width: 120px; margin-right: 10px;" />
          <el-input v-model="item.value" placeholder="偏好内容" style="flex: 1; margin-right: 10px;" />
          <el-button @click="removePreferenceItem(index)" :icon="Delete" circle />
        </div>
        <el-button @click="addPreferenceItem" type="primary" plain>
          <el-icon style="margin-right: 4px;"><Plus /></el-icon>
          添加偏好
        </el-button>
      </el-form-item>
      <el-form-item label="头像" prop="avatar">
        <el-upload
          class="avatar-uploader"
          action="#"
          :show-file-list="false"
          :before-upload="beforeAvatarUpload"
          :http-request="() => {}"
        >
          <img v-if="customerForm.avatar" :src="customerForm.avatar" class="avatar" />
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
        </el-upload>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive } from 'vue'
import { mockCustomerList } from '@/api/mockData/user'
import type { FormInstance, FormRules, UploadProps } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'

interface Customer {
  customerId: number;
  userId: number;
  realName: string;
  idCard: string | null;
  address: string;
  preference: string; // JSON string
  vipLevel: number;
  avatar: string;
}

interface PreferenceItem {
  key: string;
  value: string;
}

type CustomerFormData = Omit<Customer, 'preference' | 'idCard'> & {
  preferenceItems: PreferenceItem[];
}

const customerList = ref<Customer[]>(mockCustomerList);
const dialogVisible = ref(false);
const dialogTitle = ref('');
const dialogMode = ref<'add' | 'edit'>('add');
const customerFormRef = ref<FormInstance>();

const initialFormState: CustomerFormData = {
  customerId: 0,
  userId: 0,
  realName: '',
  address: '',
  preferenceItems: [],
  vipLevel: 0,
  avatar: '',
};
const customerForm = reactive({ ...initialFormState });

const rules = reactive<FormRules>({
  realName: [{ required: true, message: '请输入真实姓名', trigger: 'blur' }],
  address: [{ required: true, message: '请输入家庭地址', trigger: 'blur' }],
  vipLevel: [{ required: true, message: '请选择VIP等级', trigger: 'change' }],
  avatar: [{ required: true, message: '请上传头像', trigger: 'change' }],
});

const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
  const isJpgOrPng = rawFile.type === 'image/jpeg' || rawFile.type === 'image/png';
  if (!isJpgOrPng) {
    ElMessage.error('头像只能是 JPG/PNG 格式!');
    return false;
  } else if (rawFile.size / 1024 / 1024 > 2) {
    ElMessage.error('头像大小不能超过 2MB!');
    return false;
  }
  customerForm.avatar = URL.createObjectURL(rawFile);
  return false;
};

const addPreferenceItem = () => {
  customerForm.preferenceItems.push({ key: '', value: '' });
};

const removePreferenceItem = (index: number) => {
  customerForm.preferenceItems.splice(index, 1);
};

const handleAdd = () => {
  dialogMode.value = 'add';
  dialogTitle.value = '新增客户';
  Object.assign(customerForm, initialFormState);
  customerForm.preferenceItems = [];
  customerFormRef.value?.clearValidate();
  dialogVisible.value = true;
};

const handleEdit = (row: Customer) => {
  dialogMode.value = 'edit';
  dialogTitle.value = '编辑客户';
  const rowData = JSON.parse(JSON.stringify(row))
  Object.assign(customerForm, rowData);
  try {
    const preferenceObj = JSON.parse(rowData.preference || '{}');
    customerForm.preferenceItems = Object.entries(preferenceObj).map(([key, value]) => ({ key, value: String(value) }));
  } catch (e) {
    customerForm.preferenceItems = [];
  }
  dialogVisible.value = true;
};

const handleDelete = (row: Customer) => {
  ElMessageBox.confirm(`确定要删除客户 "${row.realName}" 吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    const index = customerList.value.findIndex(c => c.customerId === row.customerId);
    if (index !== -1) {
      customerList.value.splice(index, 1);
      ElMessage.success('删除成功');
      console.log('删除后，客户列表数据:', JSON.parse(JSON.stringify(customerList.value)));
    }
  }).catch(() => {});
};

const submitForm = async () => {
  if (!customerFormRef.value) return;
  await customerFormRef.value.validate((valid) => {
    if (valid) {
      const preferenceObj = Object.fromEntries(
        customerForm.preferenceItems
          .filter(item => item.key.trim() !== '')
          .map(item => [item.key, item.value])
      );
      const preferenceString = JSON.stringify(preferenceObj);

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { preferenceItems, ...customerData } = customerForm;

      if (dialogMode.value === 'add') {
        const newCustomer: Customer = {
          ...customerData,
          preference: preferenceString,
          idCard: null,
          customerId: Math.max(...customerList.value.map(c => c.customerId)) + 1,
          userId: Math.max(...customerList.value.map(c => c.userId)) + 1,
        };
        customerList.value.unshift(newCustomer);
        ElMessage.success('新增成功');
        console.log('新增后，客户列表数据:', JSON.parse(JSON.stringify(customerList.value)));
      } else {
        const index = customerList.value.findIndex(c => c.customerId === customerForm.customerId);
        if (index !== -1) {
          const updatedCustomer = {
            ...customerList.value[index],
            ...customerData,
            preference: preferenceString,
          };
          customerList.value[index] = updatedCustomer
          ElMessage.success('更新成功');
          console.log('编辑后，客户列表数据:', JSON.parse(JSON.stringify(customerList.value)));
        }
      }
      dialogVisible.value = false;
    }
  });
};

onMounted(() => {
  console.log("页面加载时，客户列表模拟数据:", JSON.parse(JSON.stringify(customerList.value)));
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}
:deep(.el-card__body) {
  flex: 1;
  overflow: hidden;
}
.el-rate {
  --el-rate-disabled-void-color: var(--el-text-color-placeholder);
}
.avatar-uploader .avatar {
  width: 120px;
  height: 120px;
  display: block;
}
</style>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  text-align: center;
}
</style> 