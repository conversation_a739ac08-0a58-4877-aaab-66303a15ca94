<template>
  <el-card class="page-card">
    <template #header>
      <div class="card-header">
        <span>报名管理</span>
        <el-button 
          type="primary" 
          @click="handleOpenRegisterDialog"
          :disabled="!selectedTraining"
        >
          <el-icon><Plus /></el-icon>
          添加报名
        </el-button>
      </div>
    </template>

    <div class="filter-container">
      <el-select v-model="selectedTraining" placeholder="请选择一个培训课程" @change="handleTrainingChange" clearable>
        <el-option
          v-for="training in allTrainings"
          :key="training.id"
          :label="training.name"
          :value="training.id"
        />
      </el-select>
    </div>

    <el-table :data="registrationTableData" style="width: 100%" height="calc(100% - 60px)">
      <el-table-column prop="registrationId" label="报名ID" width="100" />
      <el-table-column prop="cleanerName" label="家政员" width="150" />
      <el-table-column prop="cleanerPhone" label="联系电话" width="150" />
      <el-table-column prop="registrationTime" label="报名时间" width="200" />
      <el-table-column prop="status" label="报名状态" width="150">
        <template #default="{ row }">
            <el-tag :type="row.status === 'REGISTERED' ? 'success' : 'info'">
                {{ row.status === 'REGISTERED' ? '已报名' : '已取消' }}
            </el-tag>
        </template>
      </el-table-column>
       <el-table-column label="操作" fixed="right" width="120">
        <template #default="{ row }">
          <el-button 
            v-if="row.status === 'REGISTERED'"
            size="small" 
            type="warning" 
            @click="cancelRegistration(row.registrationId)">
            取消报名
          </el-button>
           <el-button 
            v-if="row.status === 'CANCELED'"
            size="small" 
            type="success" 
            @click="reRegister(row.registrationId)">
            重新报名
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-card>

  <el-dialog v-model="registerDialogVisible" title="添加报名" width="500px">
    <el-form>
      <el-form-item label="选择家政员">
        <el-select v-model="selectedCleanerForRegistration" placeholder="请选择要报名的家政员" style="width: 100%">
          <el-option
            v-for="cleaner in availableCleaners"
            :key="cleaner.cleanerId"
            :label="cleaner.realName"
            :value="cleaner.cleanerId"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="registerDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleRegisterSubmit">
          确认报名
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue';
import { mockTrainingList, mockRegistrationList, type Registration, type Training } from '@/api/mockData/training';
import { mockCleanerList } from '@/api/mockData/user';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';

const allTrainings = ref<Training[]>(JSON.parse(JSON.stringify(mockTrainingList)));
const allRegistrations = ref<Registration[]>(JSON.parse(JSON.stringify(mockRegistrationList)));
const selectedTraining = ref<number | null>(null);

const registerDialogVisible = ref(false);
const selectedCleanerForRegistration = ref<number | null>(null);

const cleanerMap = computed(() => {
    const map = new Map<number, { name: string, phone: string }>();
    mockCleanerList.forEach(c => map.set(c.cleanerId, { name: c.realName, phone: c.phone }));
    return map;
});

const availableCleaners = computed(() => {
  if (!selectedTraining.value) return [];
  
  const registeredCleanerIds = allRegistrations.value
    .filter(r => r.trainingId === selectedTraining.value)
    .map(r => r.cleanerId);

  return mockCleanerList.filter(c => !registeredCleanerIds.includes(c.cleanerId));
});

const registrationTableData = computed(() => {
  if (!selectedTraining.value) {
    return [];
  }
  return allRegistrations.value
    .filter(r => r.trainingId === selectedTraining.value)
    .map(r => ({
      ...r,
      cleanerName: cleanerMap.value.get(r.cleanerId)?.name || `家政员ID: ${r.cleanerId}`,
      cleanerPhone: cleanerMap.value.get(r.cleanerId)?.phone || '-',
    }));
});

const handleTrainingChange = (trainingId: number | null) => {
  console.log(`已选择培训课程 ID: ${trainingId}`);
};

const cancelRegistration = (registrationId: number) => {
    ElMessageBox.confirm('确定要取消该员工的报名吗?', '确认操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        const index = allRegistrations.value.findIndex(r => r.registrationId === registrationId);
        if (index !== -1) {
            const registration = allRegistrations.value[index];
            registration.status = 'CANCELED';
            
            const training = allTrainings.value.find(t => t.id === registration.trainingId);
            if (training && training.currentParticipants > 0) {
                training.currentParticipants--;
            }

            ElMessage.success('已取消该员工的报名');
            console.log('报名状态已更新:', JSON.parse(JSON.stringify(registration)));
        }
    });
};

const reRegister = (registrationId: number) => {
    const registration = allRegistrations.value.find(r => r.registrationId === registrationId);
    if (!registration) return;

    const training = allTrainings.value.find(t => t.id === registration.trainingId);
    if (training && training.currentParticipants >= training.maxParticipants) {
        ElMessage.warning('该培训报名人数已满，无法重新报名');
        return;
    }

    registration.status = 'REGISTERED';
    if (training) {
        training.currentParticipants++;
    }

    ElMessage.success('重新报名成功');
    console.log('重新报名成功:', JSON.parse(JSON.stringify(registration)));
};

const handleOpenRegisterDialog = () => {
  selectedCleanerForRegistration.value = null;
  registerDialogVisible.value = true;
};

const handleRegisterSubmit = () => {
  if (!selectedTraining.value || !selectedCleanerForRegistration.value) {
    ElMessage.warning('请选择一个培训和一个家政员');
    return;
  }
  
  const training = allTrainings.value.find(t => t.id === selectedTraining.value);
  if (training && training.currentParticipants >= training.maxParticipants) {
      ElMessage.warning('该培训报名人数已满');
      return;
  }

  const newRegistration: Registration = {
    registrationId: Math.max(...allRegistrations.value.map(r => r.registrationId), 0) + 1,
    trainingId: selectedTraining.value,
    cleanerId: selectedCleanerForRegistration.value,
    registrationTime: new Date().toLocaleString('sv-SE'),
    status: 'REGISTERED',
  };

  allRegistrations.value.push(newRegistration);
  
  if (training) {
      training.currentParticipants++;
  }

  ElMessage.success('报名成功');
  console.log('新报名创建成功:', JSON.parse(JSON.stringify(newRegistration)));
  registerDialogVisible.value = false;
};

onMounted(() => {
  // 默认选中第一个有报名的培训
  const firstTrainingWithReg = allTrainings.value.find(t => allRegistrations.value.some(r => r.trainingId === t.id));
  if (firstTrainingWithReg) {
    selectedTraining.value = firstTrainingWithReg.id;
  }
});

</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}
:deep(.el-card__body) {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.filter-container {
  margin-bottom: 20px;
}
</style> 