<template>
  <div class="customer-dashboard">
    <!-- 顶部导航栏 -->
    <header class="top-navbar">
      <div class="navbar-container">
        <div class="navbar-left">
          <div class="logo-container">
            <div class="logo-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M9 22V12H15V22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <h2 class="logo-text">家政服务</h2>
          </div>
        </div>

        <nav class="navbar-nav">
          <router-link to="/customer/dashboard" class="nav-link" :class="{ active: $route.path === '/customer/dashboard' }">
            <el-icon><House /></el-icon>
            <span>首页</span>
          </router-link>
          <router-link to="/customer/services" class="nav-link" :class="{ active: $route.path === '/customer/services' }">
            <el-icon><Grid /></el-icon>
            <span>服务预约</span>
          </router-link>
          <router-link to="/customer/orders" class="nav-link" :class="{ active: $route.path === '/customer/orders' }">
            <el-icon><List /></el-icon>
            <span>我的订单</span>
          </router-link>
          <router-link to="/customer/cleaners" class="nav-link" :class="{ active: $route.path === '/customer/cleaners' }">
            <el-icon><User /></el-icon>
            <span>家政人员</span>
          </router-link>
          <router-link to="/customer/feedback" class="nav-link" :class="{ active: $route.path === '/customer/feedback' }">
            <el-icon><ChatDotSquare /></el-icon>
            <span>评价反馈</span>
          </router-link>
        </nav>

        <div class="navbar-right">
          <el-dropdown @command="handleCommand">
            <div class="user-dropdown">
              <el-avatar :size="36" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
              <span class="user-name">李四</span>
              <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人中心
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  账户设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </header>

    <!-- 主内容区域 -->
    <main class="main-content">
      <div class="content-container">
        <router-view />
      </div>
    </main>

    <!-- 移动端底部导航 -->
    <nav class="mobile-bottom-nav">
      <router-link to="/customer/dashboard" class="mobile-nav-item" :class="{ active: $route.path === '/customer/dashboard' }">
        <el-icon><House /></el-icon>
        <span>首页</span>
      </router-link>
      <router-link to="/customer/services" class="mobile-nav-item" :class="{ active: $route.path === '/customer/services' }">
        <el-icon><Grid /></el-icon>
        <span>服务</span>
      </router-link>
      <router-link to="/customer/orders" class="mobile-nav-item" :class="{ active: $route.path === '/customer/orders' }">
        <el-icon><List /></el-icon>
        <span>订单</span>
      </router-link>
      <router-link to="/customer/profile" class="mobile-nav-item" :class="{ active: $route.path === '/customer/profile' }">
        <el-icon><User /></el-icon>
        <span>我的</span>
      </router-link>
    </nav>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { 
  House, Grid, List, User, ChatDotSquare, ArrowDown, 
  Setting, SwitchButton 
} from '@element-plus/icons-vue';

const router = useRouter();

const handleCommand = (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/customer/profile');
      break;
    case 'settings':
      router.push('/customer/settings');
      break;
    case 'logout':
      ElMessage.success('退出登录成功');
      router.push('/login');
      break;
  }
};
</script>

<style scoped>
.customer-dashboard {
  min-height: 100vh;
  background: #f5f7fa;
  display: flex;
  flex-direction: column;
}

/* 顶部导航栏 */
.top-navbar {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.navbar-left {
  display: flex;
  align-items: center;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #4f46e5, #06b6d4);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-icon svg {
  width: 24px;
  height: 24px;
  color: white;
}

.logo-text {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.navbar-nav {
  display: flex;
  align-items: center;
  gap: 32px;
  margin-left: 48px;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  color: #6b7280;
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.2s;
  font-weight: 500;
}

.nav-link:hover {
  color: #4f46e5;
  background: #f3f4f6;
}

.nav-link.active {
  color: #4f46e5;
  background: #eef2ff;
}

.navbar-right {
  display: flex;
  align-items: center;
}

.user-dropdown {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.user-dropdown:hover {
  background: #f3f4f6;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.dropdown-icon {
  width: 16px;
  height: 16px;
  color: #6b7280;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  padding: 24px 0;
}

.content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 移动端底部导航 */
.mobile-bottom-nav {
  display: none;
  background: white;
  border-top: 1px solid #e5e7eb;
  padding: 8px 0;
  position: sticky;
  bottom: 0;
  z-index: 100;
}

.mobile-nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px;
  color: #6b7280;
  text-decoration: none;
  font-size: 12px;
  transition: color 0.2s;
}

.mobile-nav-item.active {
  color: #4f46e5;
}

.mobile-nav-item .el-icon {
  font-size: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .navbar-nav {
    display: none;
  }
  
  .navbar-container {
    padding: 0 16px;
  }
  
  .content-container {
    padding: 0 16px;
  }
  
  .main-content {
    padding: 16px 0 80px 0; /* 为底部导航留出空间 */
  }
  
  .mobile-bottom-nav {
    display: flex;
  }
}

@media (max-width: 480px) {
  .logo-text {
    font-size: 18px;
  }
  
  .user-name {
    display: none;
  }
}
</style>
