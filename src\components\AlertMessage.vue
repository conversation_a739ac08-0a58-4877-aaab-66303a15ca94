<template>
  <div v-if="visible" class="alert" :class="alertClass">
    <div class="alert-icon">
      <svg v-if="type === 'success'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      <svg v-else-if="type === 'warning'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M10.29 3.86L1.82 18C1.64486 18.3024 1.55625 18.6453 1.56518 18.9928C1.57412 19.3402 1.68033 19.6781 1.87309 19.9725C2.06584 20.2669 2.33797 20.5068 2.65655 20.6675C2.97513 20.8281 3.32794 20.9041 3.68 20.89H20.32C20.6721 20.9041 21.0249 20.8281 21.3435 20.6675C21.662 20.5068 21.9342 20.2669 22.1269 19.9725C22.3197 19.6781 22.4259 19.3402 22.4348 18.9928C22.4437 18.6453 22.3551 18.3024 22.18 18L13.71 3.86C13.5317 3.56611 13.2807 3.32312 12.9812 3.15448C12.6817 2.98585 12.3438 2.89725 12 2.89725C11.6562 2.89725 11.3183 2.98585 11.0188 3.15448C10.7193 3.32312 10.4683 3.56611 10.29 3.86V3.86Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <line x1="12" y1="9" x2="12" y2="13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <line x1="12" y1="17" x2="12.01" y2="17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      <svg v-else-if="type === 'error'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      <svg v-else viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <line x1="12" y1="16" x2="12" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <line x1="12" y1="8" x2="12.01" y2="8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </div>
    
    <div class="alert-content">
      <h4 v-if="title" class="alert-title">{{ title }}</h4>
      <p class="alert-message">{{ message }}</p>
    </div>
    
    <button v-if="closable" class="alert-close" @click="close">
      <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';

interface Props {
  type?: 'success' | 'warning' | 'error' | 'info';
  title?: string;
  message: string;
  closable?: boolean;
  autoClose?: boolean;
  duration?: number;
}

const props = withDefaults(defineProps<Props>(), {
  type: 'info',
  closable: true,
  autoClose: false,
  duration: 3000,
});

const emit = defineEmits<{
  close: [];
}>();

const visible = ref(true);

const alertClass = computed(() => {
  return `alert-${props.type}`;
});

const close = () => {
  visible.value = false;
  emit('close');
};

onMounted(() => {
  if (props.autoClose) {
    setTimeout(() => {
      close();
    }, props.duration);
  }
});
</script>

<style scoped>
.alert {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  padding: var(--spacing-4);
  border-radius: var(--radius-lg);
  border-left: 4px solid;
  margin-bottom: var(--spacing-4);
  animation: slideInLeft 0.3s ease-out;
}

.alert-success {
  background: rgba(16, 185, 129, 0.1);
  border-color: var(--success-color);
  color: #065f46;
}

.alert-warning {
  background: rgba(245, 158, 11, 0.1);
  border-color: var(--warning-color);
  color: #92400e;
}

.alert-error {
  background: rgba(239, 68, 68, 0.1);
  border-color: var(--error-color);
  color: #991b1b;
}

.alert-info {
  background: rgba(59, 130, 246, 0.1);
  border-color: var(--info-color);
  color: #1e40af;
}

.alert-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  margin-top: 2px;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-size: var(--font-size-sm);
  font-weight: 600;
  margin: 0 0 var(--spacing-1) 0;
}

.alert-message {
  font-size: var(--font-size-sm);
  margin: 0;
  line-height: 1.5;
}

.alert-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-1);
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-fast);
  flex-shrink: 0;
}

.alert-close:hover {
  background: rgba(0, 0, 0, 0.1);
}

.alert-close svg {
  width: 16px;
  height: 16px;
}
</style>
