<template>
  <el-card class="page-card">
    <template #header>
      <div class="card-header">
        <span>预约列表</span>
      </div>
    </template>

    <el-table :data="bookingTableData" style="width: 100%" height="100%">
      <el-table-column prop="bookingId" label="ID" width="80" />
      <el-table-column prop="customerName" label="客户" width="120" />
      <el-table-column prop="serviceName" label="服务项目" width="150" show-overflow-tooltip />
      <el-table-column prop="serviceDate" label="服务日期" width="140" />
      <el-table-column prop="serviceTime" label="服务时间" width="160">
        <template #default="{ row }">
          {{ row.serviceTime }} - {{ row.endTime }}
        </template>
      </el-table-column>
      <el-table-column prop="address" label="服务地址" width="220" show-overflow-tooltip />
      <el-table-column prop="cleanerName" label="家政员" width="120">
        <template #default="{ row }">
          {{ row.cleanerName || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="120">
        <template #default="{ row }">
          <el-tag :type="statusMap[row.status].type" disable-transitions>
            {{ statusMap[row.status].text }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="120">
        <template #default="{ row }">
          <el-button 
            v-if="row.status === 0"
            size="small" 
            type="primary" 
            @click="handleAssign(row)">
            分配人员
          </el-button>
          <el-button v-else size="small" type="primary" plain @click="handleView(row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-card>

  <el-dialog v-model="assignDialogVisible" title="分配家政人员" width="500px">
    <el-form :model="form" ref="formRef" label-width="100px">
      <el-form-item label="客户姓名:">
        <span>{{ assignBookingInfo?.customerName }}</span>
      </el-form-item>
      <el-form-item label="服务项目:">
        <span>{{ assignBookingInfo?.serviceName }}</span>
      </el-form-item>
      <el-form-item label="服务时间:">
        <span>{{ assignBookingInfo?.serviceDate }} {{ assignBookingInfo?.serviceTime }} - {{ assignBookingInfo?.endTime }}</span>
      </el-form-item>
      <el-form-item label="选择家政员" prop="cleanerId">
        <el-select v-model="form.cleanerId" placeholder="请选择空闲的家政员">
          <el-option 
            v-for="cleaner in availableCleaners" 
            :key="cleaner.cleanerId" 
            :label="`${cleaner.realName} (评分: ${cleaner.rating})`" 
            :value="cleaner.cleanerId" 
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="assignDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitAssignment">确认分配</el-button>
    </template>
  </el-dialog>

  <el-dialog v-model="viewDialogVisible" title="查看预约详情" width="500px">
    <el-descriptions :column="1" border>
      <el-descriptions-item label="预约ID">{{ currentBookingDetails?.bookingId }}</el-descriptions-item>
      <el-descriptions-item label="预约状态">
        <el-tag :type="statusMap[currentBookingDetails?.status || 0].type" disable-transitions>
          {{ statusMap[currentBookingDetails?.status || 0].text }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="客户姓名">{{ currentBookingDetails?.customerName }}</el-descriptions-item>
      <el-descriptions-item label="客户电话">{{ currentBookingDetails?.customerPhone }}</el-descriptions-item>
      <el-descriptions-item label="服务项目">{{ currentBookingDetails?.serviceName }}</el-descriptions-item>
      <el-descriptions-item label="服务时间">{{ currentBookingDetails?.serviceDate }} {{ currentBookingDetails?.serviceTime }} - {{ currentBookingDetails?.endTime }}</el-descriptions-item>
      <el-descriptions-item label="服务地址">{{ currentBookingDetails?.address }}</el-descriptions-item>
      <el-descriptions-item label="指派人员">{{ currentBookingDetails?.cleanerName || '尚未指派' }}</el-descriptions-item>
      <el-descriptions-item label="客户备注">{{ currentBookingDetails?.remark || '无' }}</el-descriptions-item>
      <el-descriptions-item label="创建时间">{{ currentBookingDetails?.createTime }}</el-descriptions-item>
    </el-descriptions>
     <template #footer>
      <el-button type="primary" @click="viewDialogVisible = false">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { mockBookingList, type Booking } from '@/api/mockData/booking';
import { mockCustomerList } from '@/api/mockData/user';
import { mockServiceList } from '@/api/mockData/service';
import { mockCleanerList } from '@/api/mockData/user';
import { ElMessage } from 'element-plus';

// --- Data ---
const bookingList = ref<Booking[]>([]);
const assignDialogVisible = ref(false);
const viewDialogVisible = ref(false);

const form = reactive({
  bookingId: null as number | null,
  cleanerId: null as number | null,
});
const assignBookingInfo = ref<any>(null);
const currentBookingDetails = ref<any>(null);

const statusMap: { [key: number]: { text: string; type: 'warning' | 'primary' | 'info' | 'success' | 'danger' } } = {
  0: { text: '待分配', type: 'warning' },
  1: { text: '已分配', type: 'primary' },
  2: { text: '进行中', type: 'info' },
  3: { text: '已完成', type: 'success' },
  4: { text: '已取消', type: 'danger' },
};

// --- Computed ---
const customerMap = computed(() => new Map(mockCustomerList.map(c => [c.customerId, c.realName])));
const serviceMap = computed(() => new Map(mockServiceList.map(s => [s.serviceId, s.serviceName])));
const cleanerMap = computed(() => new Map(mockCleanerList.map(c => [c.cleanerId, c.realName])));

const availableCleaners = computed(() => mockCleanerList.filter(c => c.status === 0)); // 0 = 空闲

const bookingTableData = computed(() => {
  return bookingList.value.map(b => ({
    ...b,
    customerName: customerMap.value.get(b.customerId) || `客户ID:${b.customerId}`,
    serviceName: serviceMap.value.get(b.serviceId) || `服务ID:${b.serviceId}`,
    cleanerName: b.cleanerId ? (cleanerMap.value.get(b.cleanerId) || `家政员ID:${b.cleanerId}`) : null,
  }));
});

// --- Methods ---
const handleAssign = (row: any) => {
  assignBookingInfo.value = row;
  form.bookingId = row.bookingId;
  form.cleanerId = null; // Reset selection
  assignDialogVisible.value = true;
};

const handleView = (row: any) => {
  currentBookingDetails.value = row;
  viewDialogVisible.value = true;
};

const submitAssignment = () => {
  if (!form.bookingId || !form.cleanerId) {
    ElMessage.warning('请选择一位家政员');
    return;
  }
  
  const bookingIndex = bookingList.value.findIndex(b => b.bookingId === form.bookingId);
  if (bookingIndex !== -1) {
    const cleanerIndex = mockCleanerList.findIndex(c => c.cleanerId === form.cleanerId);
    if(cleanerIndex === -1){
        ElMessage.error('选择的家政员不存在');
        return;
    }
    
    // Update booking
    bookingList.value[bookingIndex].cleanerId = form.cleanerId;
    bookingList.value[bookingIndex].status = 1; // 1 = 已分配

    // Update cleaner's status
    mockCleanerList[cleanerIndex].status = 1; // 1 = 忙碌
    
    ElMessage.success('分配成功');
    console.log('分配成功，更新后的预约数据:', JSON.parse(JSON.stringify(bookingList.value[bookingIndex])));
    console.log('更新后的家政员数据:', JSON.parse(JSON.stringify(mockCleanerList[cleanerIndex])));

    assignDialogVisible.value = false;
  }
};

onMounted(() => {
  bookingList.value = JSON.parse(JSON.stringify(mockBookingList));
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}
:deep(.el-card__body) {
  flex: 1;
  overflow: hidden;
}
.el-select {
  width: 100%;
}
</style> 