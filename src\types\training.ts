/**
 * @description 培训信息
 */
export interface Training {
  id: number | string;
  name: string;
  content: string;
  startTime: string;
  endTime: string;
  location: string;
  maxParticipants: number;
  current_participants?: number;
  status: 'PLANNED' | 'ONGOING' | 'COMPLETED';
  is_deleted?: 0 | 1;
}

/**
 * @description 获取培训列表的查询参数
 */
export interface TrainingQueryParams {
  status?: string;
  startTime?: string;
  endTime?: string;
} 